using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Configurations;
using LiveCharts.Wpf;
using LiveCharts.Defaults;
using Microsoft.Extensions.DependencyInjection;
using PEMTestSystem.Models.Core;
using PEMTestSystem.Models.System;
using PEMTestSystem.Services;
using PEMTestSystem.Services.Devices;
// 明确指定Separator类型，避免冲突
using WpfSeparator = System.Windows.Controls.Separator;
using LiveChartsSeparator = LiveCharts.Wpf.Separator;

using System.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
namespace PEMTestSystem
{
    /// <summary>
    /// 设备状态的数据类，包含设备名称、连接状态、使能状态等信息。
    /// 实现 INotifyPropertyChanged 以便属性更新时界面自动刷新。
    /// </summary>
    public class DeviceStatus : INotifyPropertyChanged
    {
        /// <summary>
        /// 设备名称的后备字段。用于存储设备的显示名称。
        /// </summary>
        private string _name;
        /// <summary>
        /// 设备的显示名称。设置该属性后将触发属性变更通知以更新界面。
        /// </summary>
        public string Name
        {
            get => _name;
            set { _name = value; OnPropertyChanged(nameof(Name)); }
        }

        /// <summary>
        /// 连接状态的后备字段。表示设备当前是否与系统建立通信连接。
        /// </summary>
        private bool _isConnected;
        /// <summary>
        /// 设备是否已连接。true 表示通信正常，false 表示未连接或断开。
        /// </summary>
        public bool IsConnected
        {
            get => _isConnected;
            set { _isConnected = value; OnPropertyChanged(nameof(IsConnected)); }
        }

        /// <summary>
        /// 使能状态的后备字段。默认值为 true，表示允许设备参与实验过程。
        /// </summary>
        private bool _isEnabled = true;
        /// <summary>
        /// 设备是否被启用。禁用后设备不参与实验流程但仍可配置。
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set { _isEnabled = value; OnPropertyChanged(nameof(IsEnabled)); }
        }

        /// <summary>
        /// 是否允许禁用的后备字段。默认 true，表示该设备可在界面上进行禁用/启用切换。
        /// 某些关键设备（如直流电源、数据库）可能不允许被禁用。
        /// </summary>
        private bool _canBeDisabled = true;
        /// <summary>
        /// 指示该设备是否允许被禁用。用于控制界面中“禁用/启用”按钮的可用性。
        /// </summary>
        public bool CanBeDisabled
        {
            get => _canBeDisabled;
            set { _canBeDisabled = value; OnPropertyChanged(nameof(CanBeDisabled)); }
        }

        /// <summary>
        /// 属性变更事件。当公开属性的值变化时触发，用于通知界面刷新。
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;
        /// <summary>
        /// 触发 <see cref="PropertyChanged"/> 事件。
        /// </summary>
        /// <param name="propertyName">发生变更的属性名称。</param>
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 主窗口类，负责：
    /// 1) 展示设备连接与运行状态；
    /// 2) 接收用户输入的实验参数并进行校验；
    /// 3) 控制数据采集的启动与停止；
    /// 4) 实时显示关键测量数据并绘制曲线图；
    /// 5) 管理设备设置与配置文件的加载/保存。
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        /// <summary>
        /// 设备状态列表。用于在设备面板中展示各设备的连接与使能情况。
        /// </summary>
        public List<DeviceStatus> Devices { get; set; }

        // LiveCharts 相关属性 - 修复为正确的集合类型
        /// <summary>
        /// 图表序列集合的后备字段。存放当前模式下需要绘制的序列。
        /// </summary>
        private SeriesCollection _chartSeries;
        /// <summary>
        /// 图表序列集合。用于绑定到图表控件，展示曲线数据。
        /// </summary>
        public SeriesCollection ChartSeries
        {
            get => _chartSeries;
            set { _chartSeries = value; OnPropertyChanged(nameof(ChartSeries)); }
        }

        /// <summary>
        /// X 轴集合的后备字段。根据不同模式配置轴标题与刻度格式。
        /// </summary>
        private AxesCollection _xAxis;
        /// <summary>
        /// X 轴集合。用于图表的横轴配置（如时间或电压）。
        /// </summary>
        public AxesCollection XAxis
        {
            get => _xAxis;
            set { _xAxis = value; OnPropertyChanged(nameof(XAxis)); }
        }

        /// <summary>
        /// Y 轴集合的后备字段。根据不同模式配置轴标题与标签格式。
        /// </summary>
        private AxesCollection _yAxis;
        /// <summary>
        /// Y 轴集合。用于图表的纵轴配置（如电压或电流）。
        /// </summary>
        public AxesCollection YAxis
        {
            get => _yAxis;
            set { _yAxis = value; OnPropertyChanged(nameof(YAxis)); }
        }

        // 数据存储
        /// <summary>
        /// 电压-时间曲线数据集合（恒流模式）。X 为时间（秒），Y 为电压（伏）。
        /// </summary>
        private ChartValues<ObservablePoint> _voltageTimeData;
        /// <summary>
        /// 电流-时间曲线数据集合（恒压模式）。X 为时间（秒），Y 为电流（安）。
        /// </summary>
        private ChartValues<ObservablePoint> _currentTimeData;
        /// <summary>
        /// 电流-电压曲线数据集合（线性扫描）。X 为电压（伏），Y 为电流（安）。
        /// </summary>
        private ChartValues<ObservablePoint> _currentVoltageData;

        /// <summary>
        /// UI 线程定时器。用于在实验运行时定期驱动示例数据刷新（仅用于演示/模拟）。
        /// </summary>
        private System.Windows.Threading.DispatcherTimer _dataTimer;
        /// <summary>
        /// 实验开始时间。用于计算实验已运行时长。
        /// </summary>
        private DateTime _experimentStartTime;
        /// <summary>
        /// 当前实验模式。默认值为 <see cref="ExperimentMode.ConstantCurrent"/>。
        /// </summary>
        private ExperimentMode _currentMode = ExperimentMode.ConstantCurrent;
        /// <summary>
        /// 线扫模式的持续时间（秒）。在实验开始时计算一次并缓存，避免重复计算。
        /// 仅在线扫模式下使用，其他模式下为0。
        /// 使用锁保证线程安全。
        /// </summary>
        private double _linearScanDurationSeconds = 0;
        /// <summary>
        /// 用于保护 _linearScanDurationSeconds 字段的锁对象。
        /// </summary>
        private readonly object _linearScanDurationLock = new object();

        // Services
        /// <summary>
        /// 数据采集服务。负责采集电压、电流、温度、流量等实时数据并通过事件上报。
        /// </summary>
        private readonly DataAcquisitionService _dataAcquisitionService;
        /// <summary>
        /// 设备配置服务。负责读取与保存设备的通信与参数配置。
        /// </summary>
        private readonly DeviceConfigurationService _configService;
        /// <summary>
        /// 设备管理器。负责设备生命周期与连接状态管理。
        /// </summary>
        private readonly DeviceManager _deviceManager;

        /// <summary>
        /// 安全参数管理器。负责管理所有实验模式的安全参数配置。
        /// </summary>
        private readonly SafetyParametersManager _safetyParametersManager;

        // Current data point
        /// <summary>
        /// 最近一次接收到的数据点。用于实时显示与图表更新。
        /// </summary>
        private DataPoint? _currentDataPoint;

        // 实验状态跟踪
        /// <summary>
        /// 当前实验ID。用于跟踪正在进行的实验。
        /// </summary>
        private Guid? _currentExperimentId;
        /// <summary>
        /// 当前循环次数。用于跟踪实验进度。
        /// </summary>
        private int _currentCycle = 0;
        /// <summary>
        /// 总循环次数。从实验参数中获取。
        /// </summary>
        private int _totalCycles = 1;
        /// <summary>
        /// 单次实验持续时间（秒）。从实验参数中获取。
        /// </summary>
        private int _singleDuration = 0;
        /// <summary>
        /// 是否为无限制时间模式。
        /// </summary>
        private bool _isUnlimitedDuration = false;
        /// <summary>
        /// 实验停止原因。用于记录停止的具体原因。
        /// </summary>
        private string _stopReason = string.Empty;

        /// <summary>
        /// 实验运行模式。
        /// </summary>
        public enum ExperimentMode
        {
            /// <summary>
            /// 恒流模式：保持目标电流，观测电压随时间变化。
            /// </summary>
            ConstantCurrent,
            /// <summary>
            /// 恒压模式：保持目标电压，观测电流随时间变化。
            /// </summary>
            ConstantVoltage,
            /// <summary>
            /// 线性扫描：按电压扫描速率或总时长进行电压线性变化，记录 I-V 曲线。
            /// </summary>
            LinearScan
        }

        /// <summary>
        /// 初始化主窗口，完成控件初始化、服务获取、事件绑定、设备状态加载与图表初始化等。
        /// </summary>
        /// <remarks>
        /// 该构造函数会设置 DataContext，并从依赖注入容器中解析必要服务。
        /// </remarks>
        /// <exception cref="InvalidOperationException">当依赖注入容器中无法获取所需服务（数据采集/设备配置/设备管理）时抛出。</exception>
        public MainWindow()
        {
            InitializeComponent();

            // Get services from DI container
            var app = (App)Application.Current;
            _dataAcquisitionService = app.Host?.Services.GetRequiredService<DataAcquisitionService>()
                ?? throw new InvalidOperationException("无法获取数据采集服务");
            _configService = app.Host?.Services.GetRequiredService<DeviceConfigurationService>()
                ?? throw new InvalidOperationException("无法获取设备配置服务");
            _deviceManager = app.Host?.Services.GetRequiredService<DeviceManager>()
                ?? throw new InvalidOperationException("无法获取设备管理器");

            // 初始化安全参数管理器
            _safetyParametersManager = new SafetyParametersManager();

            InitializeServices();
            SetupEventHandlers();
            LoadDeviceStatus();
            InitializeCharts();
            InitializeDataAcquisition();
            InitializeExperimentParameters();

            // Set the DataContext for data binding
            this.DataContext = this;
        }

        /// <summary>
        /// 初始化与主窗口相关的应用服务。
        /// 功能：记录初始化日志，并预留其他服务初始化的入口。
        /// 异常：内部捕获并通过 AlarmService 记录，不向外抛出。
        /// </summary>
        private void InitializeServices()
        {
            try
            {
                App.AlarmService.Info("主窗口", "初始化服务");
                // 这里可以添加其他服务的初始化逻辑
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "服务初始化失败", ex);
            }
        }

        /// <summary>
        /// 统一设置窗口级事件处理器。
        /// 功能：调用参数文本框事件绑定方法，记录设置完成日志。
        /// 异常：内部捕获并通过 AlarmService 记录，不向外抛出。
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // 设置实验参数文本框的事件处理器
                SetupParameterTextBoxEvents();
                App.AlarmService.Info("主窗口", "事件处理器设置完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "事件处理器设置失败", ex);
            }
        }

        /// <summary>
        /// 设置实验参数输入框的事件绑定。
        /// 触发机制：本方法在窗口初始化时调用。
        /// 说明：参数 TextBox 已在 XAML 指定 TextChanged 事件，本方法仅记录调试信息。
        /// </summary>
        private void SetupParameterTextBoxEvents()
        {
            // 由于我们已经在 XAML 中为所有参数 TextBox 添加了 TextChanged="ParameterTextBox_TextChanged"
            // 这里不需要额外的设置，事件处理器会自动绑定
            App.AlarmService.Debug("主窗口", "参数文本框事件处理器已通过 XAML 绑定");
        }

        /// <summary>
        /// 初始化实验参数的默认值与状态一致性。
        /// 功能：与 XAML 默认值配合，确保界面初始状态正确。
        /// 异常：内部捕获并通过 AlarmService 记录，不向外抛出。
        /// </summary>
        private void InitializeExperimentParameters()
        {
            try
            {
                // 初始化实验参数默认值
                // 由于 XAML 中已经设置了默认值，这里主要是确保状态正确
                App.AlarmService.Info("主窗口", "实验参数初始化完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "实验参数初始化失败", ex);
            }
        }

        /// <summary>
        /// 初始化数据采集流程：订阅数据点与状态变更事件。
        /// 作用：在实验过程中接收实时数据并驱动界面更新。
        /// 异常：内部捕获并通过 AlarmService 记录，不向外抛出。
        /// </summary>
        private void InitializeDataAcquisition()
        {
            try
            {
                // 订阅数据采集事件
                _dataAcquisitionService.DataPointReceived += OnDataPointReceived;
                _dataAcquisitionService.StatusChanged += OnAcquisitionStatusChanged;

                App.AlarmService.Info("主窗口", "数据采集服务初始化完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "数据采集服务初始化失败", ex);
            }
        }

        /// <summary>
        /// 数据采集服务的数据点事件处理器。
        /// 触发条件：采集服务收到新的测量数据点。
        /// 处理逻辑：切换到 UI 线程更新界面，在后台线程检查停止条件。
        /// 对 UI 的影响：刷新关键指标文本与图表曲线。
        /// </summary>
        private void OnDataPointReceived(object? sender, DataPointEventArgs e)
        {
            try
            {
                // 在后台线程检查自动停止条件（不涉及UI操作）
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await CheckAutoStopConditionsAsync(e.DataPoint);
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Error("自动停止", "检查自动停止条件异常", ex);
                    }
                });

                // 确保在UI线程中更新界面
                Dispatcher.Invoke(() =>
                {
                    _currentDataPoint = e.DataPoint;
                    UpdateRealTimeDisplay(e.DataPoint);
                    UpdateChartData(e.DataPoint);
                });
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新实时数据显示失败", ex);
            }
        }

        /// <summary>
        /// 数据采集状态变化事件处理器。
        /// 触发条件：采集服务状态（连接、运行、错误等）发生变化。
        /// 处理逻辑：切换到 UI 线程，记录状态并可更新界面状态提示。
        /// 对 UI 的影响：状态栏或日志区域显示最新采集状态。
        /// </summary>
        private void OnAcquisitionStatusChanged(object? sender, AcquisitionStatusEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // 更新状态显示
                    App.AlarmService.Info("数据采集", $"状态变更: {e.Message}");
                });
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新采集状态失败", ex);
            }
        }

        /// <summary>
        /// 使用最新数据点更新界面实时数值与设备状态指示灯。
        /// 参数：
        /// - dataPoint: 当前采集到的测量数据（电压、电流、温度、流量等）。
        /// 异常：内部捕获并报警，不影响程序继续运行。
        /// </summary>
        private void UpdateRealTimeDisplay(DataPoint dataPoint)
        {
            try
            {
                // 更新实时数据显示
                VoltageValueText.Text = $"{dataPoint.Voltage:F3} V";
                CurrentValueText.Text = $"{dataPoint.Current:F2} A";
                TemperatureValueText.Text = $"{dataPoint.Temperature:F1} °C";
                FlowRate1ValueText.Text = $"{dataPoint.FlowRate1:F2} mL/min";
                FlowRate2ValueText.Text = $"{dataPoint.FlowRate2:F2} mL/min";

                // 更新设备状态指示器
                UpdateDeviceStatusIndicators();
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新实时显示失败", ex);
            }
        }





        /// <summary>
        /// 更新设备状态指示器（颜色/样式等）。
        /// 功能：根据设备管理器提供的连接状态更新 UI 指示。
        /// 说明：当前为占位实现，后续接入真实设备状态。
        /// </summary>
        private void UpdateDeviceStatusIndicators()
        {
            try
            {
                // 这里应该从设备管理器获取实际的连接状态
                // 暂时使用模拟状态
                var successBrush = FindResource("SuccessColor") as SolidColorBrush;
                var dangerBrush = FindResource("DangerColor") as SolidColorBrush;

            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新设备状态指示器失败", ex);
            }
        }

        /// <summary>
        /// 根据当前实验模式将数据点写入对应的曲线集合，并限制集合长度。
        /// 参数：
        /// - dataPoint: 实时数据点，包含时间、电压与电流等。
        /// 返回：无。
        /// 异常：内部捕获并记录日志，不向外抛出。
        /// </summary>
        private void UpdateChartData(DataPoint dataPoint)
        {
            try
            {
                var elapsedSeconds = (double)dataPoint.ElapsedSeconds;

                // 根据当前模式更新图表数据
                switch (_currentMode)
                {
                    case ExperimentMode.ConstantCurrent:
                        // 恒流模式：显示电压-时间
                        _voltageTimeData.Add(new ObservablePoint(elapsedSeconds, (double)dataPoint.Voltage));
                        break;

                    case ExperimentMode.ConstantVoltage:
                        // 恒压模式：显示电流-时间
                        _currentTimeData.Add(new ObservablePoint(elapsedSeconds, (double)dataPoint.Current));
                        break;

                    case ExperimentMode.LinearScan:
                        // 线扫模式：显示电流-电压
                        _currentVoltageData.Add(new ObservablePoint((double)dataPoint.Voltage, (double)dataPoint.Current));
                        break;
                }

                // 限制数据点数量以避免内存问题
                const int maxDataPoints = 1000;
                if (_voltageTimeData.Count > maxDataPoints)
                {
                    _voltageTimeData.RemoveAt(0);
                }
                if (_currentTimeData.Count > maxDataPoints)
                {
                    _currentTimeData.RemoveAt(0);
                }
                if (_currentVoltageData.Count > maxDataPoints)
                {
                    _currentVoltageData.RemoveAt(0);
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新图表数据失败", ex);
            }
        }

        /// <summary>
        /// 初始化图表相关的数据结构与控件绑定：
        /// 1) 创建三种模式对应的数据集合；
        /// 2) 生成示例默认数据以便初始显示；
        /// 3) 默认配置为恒流模式的图表；
        /// 4) 启动用于演示的 UI 定时器（不影响真实采集流程）。
        /// </summary>
        private void InitializeCharts()
        {
            // 初始化数据集合
            _voltageTimeData = new ChartValues<ObservablePoint>();
            _currentTimeData = new ChartValues<ObservablePoint>();
            _currentVoltageData = new ChartValues<ObservablePoint>();

            // 添加默认数据以便显示
            GenerateDefaultData();

            // 初始化为恒流模式图表
            SetupConstantCurrentChart();

            // 设置数据更新定时器
            _dataTimer = new System.Windows.Threading.DispatcherTimer();
            _dataTimer.Interval = TimeSpan.FromSeconds(1);
            _dataTimer.Tick += DataTimer_Tick;
        }

        /// <summary>
        /// 生成三种模式的默认示例数据，用于界面初始展示：
        /// - 恒流模式：电压-时间曲线（V-t）；
        /// - 恒压模式：电流-时间曲线（I-t）；
        /// - 线性扫描：电流-电压曲线（I-V）。
        /// </summary>
        private void GenerateDefaultData()
        {
            // 生成恒流模式默认数据 (电压-时间)
            for (int i = 0; i <= 50; i++)
            {
                var time = i * 10.0; // 时间点
                var voltage = 1.8 + 0.05 * Math.Sin(time * 0.05) + (new Random(i).NextDouble() - 0.5) * 0.02;
                _voltageTimeData.Add(new ObservablePoint(time, voltage));
            }

            // 生成恒压模式默认数据 (电流-时间)
            for (int i = 0; i <= 50; i++)
            {
                var time = i * 10.0;
                var current = 20 + 1.5 * Math.Sin(time * 0.03) + (new Random(i + 100).NextDouble() - 0.5) * 0.8;
                _currentTimeData.Add(new ObservablePoint(time, current));
            }

            // 生成线扫模式默认数据 (电流-电压)
            for (int i = 0; i <= 50; i++)
            {
                var voltage = 1.2 + i * 0.02; // 电压从1.2V到2.2V
                var current = Math.Max(0, (voltage - 1.2) * 25 + (new Random(i + 200).NextDouble() - 0.5) * 2);
                _currentVoltageData.Add(new ObservablePoint(voltage, current));
            }
        }

        /// <summary>
        /// 配置恒流模式的图表：
        /// - X 轴显示时间（秒），Y 轴显示电压（伏）；
        /// - 绑定电压-时间数据序列；
        /// - 更新图表标题为恒流模式说明。
        /// </summary>
        private void SetupConstantCurrentChart()
        {
            ChartSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "电压",
                    Values = _voltageTimeData,
                    PointGeometry = null,
                    LineSmoothness = 0.3,
                    Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#00aaff")),
                    Fill = Brushes.Transparent,
                    StrokeThickness = 2
                }
            };

            XAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "时间 (s)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    Separator = new LiveChartsSeparator
                    {
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            YAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "电压 (V)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    LabelFormatter = value => value.ToString("F2"),
                    Separator = new LiveChartsSeparator
                    {
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            if (FindName("ChartTitleText") is TextBlock titleText)
            {
                titleText.Text = "恒流模式: 电压-时间 (V-t) 曲线";
            }
        }

        /// <summary>
        /// 配置恒压模式的图表：
        /// - X 轴显示时间（秒），Y 轴显示电流（安）；
        /// - 绑定电流-时间数据序列；
        /// - 更新图表标题为恒压模式说明。
        /// </summary>
        private void SetupConstantVoltageChart()
        {
            ChartSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "电流",
                    Values = _currentTimeData,
                    PointGeometry = null,
                    LineSmoothness = 0.3,
                    Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#00ff9b")),
                    Fill = Brushes.Transparent,
                    StrokeThickness = 2
                }
            };

            XAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "时间 (s)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    Separator = new LiveChartsSeparator
                    {
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            YAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "电流 (A)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    LabelFormatter = value => value.ToString("F2"),
                    Separator = new LiveChartsSeparator
                    {
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            if (FindName("ChartTitleText") is TextBlock titleText)
            {
                titleText.Text = "恒压模式: 电流-时间 (I-t) 曲线";
            }
        }

        /// <summary>
        /// 配置线性扫描模式的图表：
        /// - X 轴显示电压（伏），Y 轴显示电流（安）；
        /// - 绑定电流-电压数据序列；
        /// - 更新图表标题为线性扫描说明。
        /// </summary>
        private void SetupLinearScanChart()
        {
            ChartSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "I-V曲线",
                    Values = _currentVoltageData,
                    PointGeometry = null,
                    LineSmoothness = 0.3,
                    Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#ff4d4d")),
                    Fill = Brushes.Transparent,
                    StrokeThickness = 2
                }
            };

            XAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "电压 (V)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    Separator = new LiveChartsSeparator
                    {
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            YAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "电流 (A)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    LabelFormatter = value => value.ToString("F2"),
                    Separator = new LiveChartsSeparator
                    {
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            if (FindName("ChartTitleText") is TextBlock titleText)
            {
                titleText.Text = "线性扫描: 电流-电压 (I-V) 曲线";
            }
        }

        /// <summary>
        /// 实验模式 Tab 选中项变化事件处理器。
        /// 触发条件：用户切换 TabItem。
        /// 处理逻辑：根据所选标签的 Tag 切换当前模式并重新配置图表显示。
        /// 对 UI 的影响：更新曲线序列、轴标题与图表标题。
        /// </summary>
        private void ExperimentModeTabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is TabControl tabControl &&
                tabControl.SelectedItem is TabItem selectedTab &&
                selectedTab.Tag != null)
            {
                switch (selectedTab.Tag.ToString())
                {
                    case "ConstantCurrent":
                        _currentMode = ExperimentMode.ConstantCurrent;
                        SetupConstantCurrentChart();
                        UpdateUnlimitedUIState();
                        break;
                    case "ConstantVoltage":
                        _currentMode = ExperimentMode.ConstantVoltage;
                        SetupConstantVoltageChart();
                        UpdateUnlimitedUIState();
                        break;
                    case "LinearScan":
                        _currentMode = ExperimentMode.LinearScan;
                        SetupLinearScanChart();
                        break;
                }
            }
        }

        /// <summary>
        /// UI 定时器的 Tick 事件处理器（演示用）。
        /// 触发条件：定时器到期且实验处于“进行中”。
        /// 处理逻辑：按当前模式向对应数据集合追加示例点，并控制集合长度。
        /// 对 UI 的影响：实时曲线将随数据集合变化自动刷新。
        /// </summary>
        private void DataTimer_Tick(object sender, EventArgs e)
        {
            if (FindName("StartButton") is Button startBtn && !startBtn.IsEnabled) // 实验进行中
            {
                var elapsedTime = (DateTime.Now - _experimentStartTime).TotalSeconds;
                var random = new Random();

                switch (_currentMode)
                {
                    case ExperimentMode.ConstantCurrent:
                        var voltage = 1.8 + 0.1 * Math.Sin(elapsedTime * 0.1) + (random.NextDouble() - 0.5) * 0.05;
                        _voltageTimeData.Add(new ObservablePoint(elapsedTime, voltage));
                        if (_voltageTimeData.Count > 100) _voltageTimeData.RemoveAt(0);
                        break;

                    case ExperimentMode.ConstantVoltage:
                        var current = 20 + 2 * Math.Sin(elapsedTime * 0.05) + (random.NextDouble() - 0.5) * 1;
                        _currentTimeData.Add(new ObservablePoint(elapsedTime, current));
                        if (_currentTimeData.Count > 100) _currentTimeData.RemoveAt(0);
                        break;

                    case ExperimentMode.LinearScan:
                        var scanVoltage = 1.2 + (elapsedTime * 0.01);
                        var scanCurrent = Math.Max(0, (scanVoltage - 1.2) * 20 + (random.NextDouble() - 0.5) * 2);
                        _currentVoltageData.Add(new ObservablePoint(scanVoltage, scanCurrent));
                        if (_currentVoltageData.Count > 100) _currentVoltageData.RemoveAt(0);
                        break;
                }
            }
        }

        /// <summary>
        /// 加载设备状态列表到界面。
        /// 功能：构造包含电源、温控仪、流量泵、数据库等设备的初始状态集合。
        /// 说明：当前为示例数据，后续可由设备管理器提供真实状态。
        /// </summary>
        private void LoadDeviceStatus()
        {
            Devices = new List<DeviceStatus>
            {
                new DeviceStatus { Name = "直流电源", IsConnected = true, CanBeDisabled = false },
                new DeviceStatus { Name = "温控仪", IsConnected = true, CanBeDisabled = true },
                new DeviceStatus { Name = "流量泵 1", IsConnected = true, CanBeDisabled = true },
                new DeviceStatus { Name = "流量泵 2", IsConnected = false, CanBeDisabled = true },
                new DeviceStatus { Name = "数据库", IsConnected = true, CanBeDisabled = false }
            };
        }

        /// <summary>
        /// 设备面板中“启用/禁用”按钮的点击事件处理器。
        /// 触发条件：用户点击设备行上的启用/禁用按钮。
        /// 处理逻辑：切换对应设备的 IsEnabled 状态。
        /// 对 UI 的影响：按钮文字与行样式随状态变化更新。
        /// </summary>
        private void ToggleDeviceState_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is DeviceStatus device)
            {
                device.IsEnabled = !device.IsEnabled;
            }
        }

        /// <summary>
        /// 获取并验证当前实验参数。
        /// 功能：根据当前模式从界面控件中获取实验参数，并进行基本验证。
        /// 返回：true 表示参数有效，false 表示参数无效。
        /// </summary>
        private bool GetAndValidateExperimentParameters()
        {
            try
            {
                switch (_currentMode)
                {
                    case ExperimentMode.ConstantCurrent:
                        return GetConstantCurrentParameters();
                    case ExperimentMode.ConstantVoltage:
                        return GetConstantVoltageParameters();
                    case ExperimentMode.LinearScan:
                        return GetLinearScanParameters();
                    default:
                        App.AlarmService.Error("参数验证", "未知的实验模式");
                        return false;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "获取实验参数失败", ex);
                MessageBox.Show($"获取实验参数失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 获取恒流模式参数。
        /// </summary>
        private bool GetConstantCurrentParameters()
        {
            try
            {
                // 获取循环次数
                if (FindName("ConstantCurrentRepeatCountBox") is TextBox repeatBox &&
                    int.TryParse(repeatBox.Text, out int repeatCount) && repeatCount > 0)
                {
                    _totalCycles = repeatCount;
                }
                else
                {
                    MessageBox.Show("请输入有效的重复次数（大于0的整数）", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                // 获取持续时间
                var unlimitedCheckBox = FindName("ConstantCurrentUnlimitedCheckBox") as CheckBox;
                _isUnlimitedDuration = unlimitedCheckBox?.IsChecked == true;

                if (!_isUnlimitedDuration)
                {
                    if (FindName("ConstantCurrentDurationBox") is TextBox durationBox &&
                        int.TryParse(durationBox.Text, out int duration) && duration > 0)
                    {
                        _singleDuration = duration;
                    }
                    else
                    {
                        MessageBox.Show("请输入有效的持续时间（大于0的整数秒）", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }
                }

                App.AlarmService.Info("参数验证", $"恒流模式参数 - 循环次数: {_totalCycles}, 持续时间: {(_isUnlimitedDuration ? "无限制" : $"{_singleDuration}秒")}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "获取恒流模式参数失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取恒压模式参数。
        /// </summary>
        private bool GetConstantVoltageParameters()
        {
            try
            {
                // 获取循环次数
                if (FindName("ConstantVoltageRepeatCountBox") is TextBox repeatBox &&
                    int.TryParse(repeatBox.Text, out int repeatCount) && repeatCount > 0)
                {
                    _totalCycles = repeatCount;
                }
                else
                {
                    MessageBox.Show("请输入有效的重复次数（大于0的整数）", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                // 获取持续时间
                var unlimitedCheckBox = FindName("ConstantVoltageUnlimitedCheckBox") as CheckBox;
                _isUnlimitedDuration = unlimitedCheckBox?.IsChecked == true;

                if (!_isUnlimitedDuration)
                {
                    if (FindName("ConstantVoltageDurationBox") is TextBox durationBox &&
                        int.TryParse(durationBox.Text, out int duration) && duration > 0)
                    {
                        _singleDuration = duration;
                    }
                    else
                    {
                        MessageBox.Show("请输入有效的持续时间（大于0的整数秒）", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }
                }

                App.AlarmService.Info("参数验证", $"恒压模式参数 - 循环次数: {_totalCycles}, 持续时间: {(_isUnlimitedDuration ? "无限制" : $"{_singleDuration}秒")}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "获取恒压模式参数失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取线扫模式参数。
        /// </summary>
        private bool GetLinearScanParameters()
        {
            try
            {
                // 获取循环次数
                if (FindName("LinearScanRepeatCountBox") is TextBox repeatBox &&
                    int.TryParse(repeatBox.Text, out int repeatCount) && repeatCount > 0)
                {
                    _totalCycles = repeatCount;
                }
                else
                {
                    MessageBox.Show("请输入有效的重复次数（大于0的整数）", "参数错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                // 线扫模式没有持续时间设置，由扫描参数决定
                _isUnlimitedDuration = false;
                _singleDuration = 0; // 将由扫描速率和电压范围计算

                App.AlarmService.Info("参数验证", $"线扫模式参数 - 循环次数: {_totalCycles}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "获取线扫模式参数失败", ex);
                return false;
            }
        }
        /// <summary>
        /// “开始实验”按钮点击事件处理器。
        /// 触发条件：用户点击开始按钮。
        /// 主要步骤：
        /// 1) 置开始按钮不可用并记录实验起始时间；
        /// 2) 按当前模式清空曲线数据；
        /// 3) 调用采集服务开始采集；
        /// 4) 成功则启用停止按钮并启动演示定时器，失败则恢复按钮状态并提示。
        /// 对 UI 的影响：按钮可用性、消息提示、图表数据将更新。
        /// </summary>
        private async void StartButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (FindName("StartButton") is Button startBtn && FindName("StopButton") is Button stopBtn)
                {
                    startBtn.IsEnabled = false;

                    // 获取并验证实验参数
                    if (!GetAndValidateExperimentParameters())
                    {
                        startBtn.IsEnabled = true;
                        return;
                    }

                    // 创建新的实验记录
                    var experimentId = Guid.NewGuid();
                    _currentExperimentId = experimentId;
                    _experimentStartTime = DateTime.Now;
                    _currentCycle = 0;

                    // 如果是线扫模式，预先计算并缓存持续时间
                    if (_currentMode == ExperimentMode.LinearScan)
                    {
                        var calculatedDuration = CalculateLinearScanDuration();
                        lock (_linearScanDurationLock)
                        {
                            _linearScanDurationSeconds = calculatedDuration;
                            if (_linearScanDurationSeconds <= 0)
                            {
                                App.AlarmService.Warning("线扫参数", "无法计算线扫持续时间，使用默认值120秒");
                                _linearScanDurationSeconds = 120; // 仅作为备用值
                            }
                        }
                        App.AlarmService.Info("线扫计算", $"线扫持续时间已缓存: {calculatedDuration:F1}秒");
                    }
                    else
                    {
                        lock (_linearScanDurationLock)
                        {
                            _linearScanDurationSeconds = 0; // 非线扫模式重置为0
                        }
                    }

                    // 清空当前数据，开始实时更新
                    switch (_currentMode)
                    {
                        case ExperimentMode.ConstantCurrent:
                            _voltageTimeData.Clear();
                            break;
                        case ExperimentMode.ConstantVoltage:
                            _currentTimeData.Clear();
                            break;
                        case ExperimentMode.LinearScan:
                            _currentVoltageData.Clear();
                            break;
                    }

                    // 启动数据采集
                    var success = await _dataAcquisitionService.StartAcquisitionAsync(experimentId);
                    if (success)
                    {
                        stopBtn.IsEnabled = true;
                        _dataTimer.Start();
                        App.AlarmService.Info("实验控制", $"实验已开始 - 模式: {_currentMode}, 循环次数: {_totalCycles}, 持续时间: {(_isUnlimitedDuration ? "无限制" : $"{_singleDuration}秒")}");
                        MessageBox.Show("实验开始！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        startBtn.IsEnabled = true;
                        _currentExperimentId = null;
                        MessageBox.Show("启动实验失败，请检查设备连接状态！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("实验控制", "启动实验异常", ex);
                MessageBox.Show($"启动实验失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 恢复按钮状态
                if (FindName("StartButton") is Button startBtn)
                {
                    startBtn.IsEnabled = true;
                }
                _currentExperimentId = null;
            }
        }

        /// <summary>
        /// “停止实验”按钮点击事件处理器。
        /// 触发条件：用户点击停止按钮。
        /// 主要步骤：
        /// 1) 置停止按钮不可用并停止演示定时器；
        /// 2) 调用采集服务停止采集；
        /// 3) 成功则恢复开始按钮并提示，失败则恢复停止按钮并提示错误。
        /// 对 UI 的影响：按钮可用性与提示信息更新。
        /// </summary>
        private async void StopButton_Click(object sender, RoutedEventArgs e)
        {
            await StopExperimentAsync("用户手动停止");
        }

        /// <summary>
        /// 通用的实验停止方法。
        /// 功能：执行完整的实验停止流程，包括设备停止、状态更新、资源清理等。
        /// 参数：stopReason - 停止原因，用于日志记录和用户提示。
        /// 线程安全：使用 Dispatcher.Invoke 确保 UI 更新在主线程中执行。
        /// </summary>
        private async Task StopExperimentAsync(string stopReason)
        {
            try
            {
                // 确保在UI线程中更新界面
                await Dispatcher.InvokeAsync(async () =>
                {
                    var startBtn = FindName("StartButton") as Button;
                    var stopBtn = FindName("StopButton") as Button;

                    if (startBtn == null || stopBtn == null)
                    {
                        App.AlarmService.Error("实验控制", "无法找到开始或停止按钮控件");
                        return;
                    }

                    // 防止重复停止操作
                    if (!stopBtn.IsEnabled)
                    {
                        App.AlarmService.Warning("实验控制", "实验停止操作已在进行中");
                        return;
                    }

                    // 禁用停止按钮，防止重复点击
                    stopBtn.IsEnabled = false;
                    _stopReason = stopReason;

                    App.AlarmService.Info("实验控制", $"开始停止实验 - 原因: {stopReason}");

                    try
                    {
                        // 1. 停止UI定时器
                        _dataTimer?.Stop();

                        // 2. 停止数据采集服务
                        var acquisitionStopped = await _dataAcquisitionService.StopAcquisitionAsync();
                        if (!acquisitionStopped)
                        {
                            App.AlarmService.Warning("实验控制", "数据采集服务停止失败，继续执行其他停止操作");
                        }

                        // 3. 停止所有设备
                        await StopAllDevicesAsync();

                        // 4. 更新UI状态
                        UpdateUIAfterStop();

                        // 5. 清理实验状态
                        CleanupExperimentState();

                        // 6. 记录停止日志
                        var elapsedTime = _experimentStartTime != default ?
                            (DateTime.Now - _experimentStartTime).TotalSeconds : 0;

                        App.AlarmService.Info("实验控制",
                            $"实验已停止 - 原因: {stopReason}, 运行时间: {elapsedTime:F1}秒, 当前循环: {_currentCycle}/{_totalCycles}");

                        // 7. 显示用户提示
                        ShowStopMessage(stopReason, elapsedTime);
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Error("实验控制", $"停止实验过程中发生异常: {ex.Message}", ex);

                        // 即使发生异常，也要确保UI状态正确
                        UpdateUIAfterStop();
                        CleanupExperimentState();

                        MessageBox.Show($"停止实验时发生错误: {ex.Message}\n\n实验已强制停止。",
                                      "停止异常", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                });
            }
            catch (Exception ex)
            {
                App.AlarmService.Fatal("实验控制", "停止实验的关键异常", ex);

                // 最后的安全措施：强制重置UI状态
                try
                {
                    Dispatcher.Invoke(() =>
                    {
                        var startBtn = FindName("StartButton") as Button;
                        var stopBtn = FindName("StopButton") as Button;
                        if (startBtn != null) startBtn.IsEnabled = true;
                        if (stopBtn != null) stopBtn.IsEnabled = false;
                    });
                }
                catch
                {
                    // 如果连UI更新都失败了，记录到控制台
                    System.Console.WriteLine($"严重错误：无法更新UI状态 - {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 停止所有设备的运行。
        /// 功能：通过设备管理器停止所有启用的设备。
        /// 异常：内部捕获并记录，不向外抛出。
        /// </summary>
        private async Task StopAllDevicesAsync()
        {
            try
            {
                App.AlarmService.Info("设备控制", "开始停止所有设备");

                // 通过设备管理器停止所有设备
                var success = await _deviceManager.DisconnectAllDevicesAsync();

                if (success)
                {
                    App.AlarmService.Info("设备控制", "所有设备已成功停止");
                }
                else
                {
                    App.AlarmService.Warning("设备控制", "部分设备停止失败");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备控制", "停止设备时发生异常", ex);
            }
        }

        /// <summary>
        /// 更新实验停止后的UI状态。
        /// 功能：恢复按钮状态，更新状态显示。
        /// </summary>
        private void UpdateUIAfterStop()
        {
            try
            {
                var startBtn = FindName("StartButton") as Button;
                var stopBtn = FindName("StopButton") as Button;

                if (startBtn != null) startBtn.IsEnabled = true;
                if (stopBtn != null) stopBtn.IsEnabled = false;

                // 更新设备状态指示器
                UpdateDeviceStatusIndicators();

                App.AlarmService.Debug("界面更新", "实验停止后UI状态已更新");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("界面更新", "更新停止后UI状态失败", ex);
            }
        }

        /// <summary>
        /// 清理实验状态和相关资源。
        /// 功能：重置实验相关的状态变量，清理定时器等资源。
        /// </summary>
        private void CleanupExperimentState()
        {
            try
            {
                _currentExperimentId = null;
                _currentCycle = 0;
                _totalCycles = 1;
                _singleDuration = 0;
                _isUnlimitedDuration = false;
                _stopReason = string.Empty;

                // 清理线扫持续时间缓存
                lock (_linearScanDurationLock)
                {
                    _linearScanDurationSeconds = 0;
                }

                // 确保定时器已停止
                _dataTimer?.Stop();

                App.AlarmService.Debug("状态清理", "实验状态已清理");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("状态清理", "清理实验状态失败", ex);
            }
        }

        /// <summary>
        /// 显示实验停止的用户提示消息。
        /// 参数：
        /// - stopReason: 停止原因
        /// - elapsedTime: 实验运行时间（秒）
        /// </summary>
        private void ShowStopMessage(string stopReason, double elapsedTime)
        {
            try
            {
                string message;
                MessageBoxImage icon;

                switch (stopReason)
                {
                    case "用户手动停止":
                        message = $"实验已手动停止。\n\n运行时间: {elapsedTime:F1}秒\n完成循环: {_currentCycle}/{_totalCycles}";
                        icon = MessageBoxImage.Information;
                        break;
                    case "实验完成":
                        message = $"实验已完成！\n\n总运行时间: {elapsedTime:F1}秒\n完成循环: {_totalCycles}/{_totalCycles}";
                        icon = MessageBoxImage.Information;
                        break;
                    case "系统故障":
                        message = $"检测到系统故障，实验已自动停止。\n\n运行时间: {elapsedTime:F1}秒\n请检查设备状态。";
                        icon = MessageBoxImage.Warning;
                        break;
                    default:
                        message = $"实验已停止。\n\n停止原因: {stopReason}\n运行时间: {elapsedTime:F1}秒";
                        icon = MessageBoxImage.Information;
                        break;
                }

                MessageBox.Show(message, "实验停止", MessageBoxButton.OK, icon);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("界面提示", "显示停止消息失败", ex);
                // 备用简单提示
                MessageBox.Show("实验已停止。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 检查自动停止条件。
        /// 功能：根据实验模式和参数检查是否满足自动停止条件。
        /// 参数：dataPoint - 当前数据点，用于计算实验进度。
        /// </summary>
        private async Task CheckAutoStopConditionsAsync(DataPoint dataPoint)
        {
            try
            {
                // 如果实验未在运行，直接返回
                if (_currentExperimentId == null || _experimentStartTime == default)
                {
                    return;
                }

                var elapsedSeconds = (DateTime.Now - _experimentStartTime).TotalSeconds;
                bool shouldStop = false;
                string stopReason = string.Empty;

                // 检查不同模式的停止条件
                switch (_currentMode)
                {
                    case ExperimentMode.ConstantCurrent:
                    case ExperimentMode.ConstantVoltage:
                        shouldStop = CheckConstantModeStopConditions(elapsedSeconds, out stopReason);
                        break;

                    case ExperimentMode.LinearScan:
                        shouldStop = CheckLinearScanStopConditions(elapsedSeconds, out stopReason);
                        break;
                }

                // 检查系统故障条件
                if (!shouldStop)
                {
                    shouldStop = CheckSystemFaultConditions(dataPoint, out stopReason);
                }

                // 如果满足停止条件，执行自动停止
                if (shouldStop)
                {
                    App.AlarmService.Warning("自动停止", $"检测到停止条件: {stopReason}");
                    await StopExperimentAsync(stopReason);
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("自动停止", "检查自动停止条件时发生异常", ex);
            }
        }

        /// <summary>
        /// 检查恒流/恒压模式的停止条件。
        /// 返回：是否应该停止实验和停止原因。
        /// </summary>
        private bool CheckConstantModeStopConditions(double elapsedSeconds, out string stopReason)
        {
            stopReason = string.Empty;

            try
            {
                // 如果是无限制时间模式，只检查循环次数
                if (_isUnlimitedDuration)
                {
                    // 无限制时间模式下，只有手动停止或故障才会停止
                    return false;
                }

                // 检查单次实验时间是否到达
                if (elapsedSeconds >= _singleDuration)
                {
                    _currentCycle++;
                    App.AlarmService.Info("实验进度", $"完成第 {_currentCycle} 次循环，总共 {_totalCycles} 次");

                    // 检查是否完成所有循环
                    if (_currentCycle >= _totalCycles)
                    {
                        stopReason = "实验完成";
                        return true;
                    }
                    else
                    {
                        // 重置实验开始时间，开始下一个循环
                        _experimentStartTime = DateTime.Now;
                        var nextCycleNumber = _currentCycle + 1;
                        App.AlarmService.Info("实验控制", $"开始第 {nextCycleNumber} 次循环");
                        return false;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("停止条件检查", "检查恒流/恒压模式停止条件失败", ex);
                stopReason = "检查停止条件异常";
                return true; // 出现异常时停止实验以确保安全
            }
        }

        /// <summary>
        /// 检查线扫模式的停止条件。
        /// 返回：是否应该停止实验和停止原因。
        /// </summary>
        private bool CheckLinearScanStopConditions(double elapsedSeconds, out string stopReason)
        {
            stopReason = string.Empty;

            try
            {
                // 使用缓存的线扫持续时间，避免重复计算
                double scanDurationSeconds;
                lock (_linearScanDurationLock)
                {
                    scanDurationSeconds = _linearScanDurationSeconds;
                }

                if (scanDurationSeconds <= 0)
                {
                    // 从界面获取默认持续时间
                    double defaultDuration = 120; // 硬编码备用值
                    if (FindName("LinearScanDefaultDurationBox") is TextBox defaultDurationBox &&
                        double.TryParse(defaultDurationBox.Text, out double userDefaultDuration) &&
                        userDefaultDuration > 0)
                    {
                        defaultDuration = userDefaultDuration;
                    }

                    App.AlarmService.Warning("线扫参数", $"缓存的线扫持续时间无效，使用默认值{defaultDuration}秒");
                    scanDurationSeconds = defaultDuration;
                }

                if (elapsedSeconds >= scanDurationSeconds)
                {
                    _currentCycle++;
                    App.AlarmService.Info("实验进度", $"完成第 {_currentCycle} 次线扫，总共 {_totalCycles} 次");

                    // 检查是否完成所有循环
                    if (_currentCycle >= _totalCycles)
                    {
                        stopReason = "实验完成";
                        return true;
                    }
                    else
                    {
                        // 重置实验开始时间，开始下一个循环
                        _experimentStartTime = DateTime.Now;
                        var nextCycleNumber = _currentCycle + 1;
                        App.AlarmService.Info("实验控制", $"开始第 {nextCycleNumber} 次线扫");
                        return false;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("停止条件检查", "检查线扫模式停止条件失败", ex);
                stopReason = "检查停止条件异常";
                return true;
            }
        }

        /// <summary>
        /// 计算线性扫描的持续时间。
        /// 返回：扫描持续时间（秒），包含变化时间和终点保持时间。
        /// </summary>
        private double CalculateLinearScanDuration()
        {
            try
            {
                // 获取起始和终点电压
                var startVoltageText = (FindName("LinearScanStartVoltageBox") as TextBox)?.Text ?? "0";
                var endVoltageText = (FindName("LinearScanEndVoltageBox") as TextBox)?.Text ?? "0";
                var holdTimeText = (FindName("LinearScanHoldTimeBox") as TextBox)?.Text ?? "0";

                if (!double.TryParse(startVoltageText, out double startVoltage) ||
                    !double.TryParse(endVoltageText, out double endVoltage) ||
                    !double.TryParse(holdTimeText, out double holdTime))
                {
                    App.AlarmService.Warning("参数解析", "无法解析线扫电压或保持时间参数");
                    return 0;
                }

                double voltageRange = Math.Abs(endVoltage - startVoltage);
                double scanTime = 0;

                // 检查扫描方式
                var scanByRateRadio = FindName("ScanByRateRadio") as RadioButton;
                bool isScanByRate = scanByRateRadio?.IsChecked == true;

                if (isScanByRate)
                {
                    // 按速率模式：时间 = 电压范围 / 变化斜率
                    var rateText = (FindName("LinearScanRateBox") as TextBox)?.Text ?? "0";
                    if (double.TryParse(rateText, out double rate) && rate > 0)
                    {
                        scanTime = voltageRange / rate;
                    }
                    else
                    {
                        App.AlarmService.Warning("参数解析", "无法解析变化斜率参数或斜率为0");
                        return 0;
                    }
                }
                else
                {
                    // 按时间模式：直接使用设定的变化时间
                    var timeText = (FindName("LinearScanTimeBox") as TextBox)?.Text ?? "0";
                    if (double.TryParse(timeText, out double time) && time > 0)
                    {
                        scanTime = time;
                    }
                    else
                    {
                        App.AlarmService.Warning("参数解析", "无法解析变化时间参数或时间为0");
                        return 0;
                    }
                }

                // 总持续时间 = 扫描时间 + 终点保持时间
                double totalDuration = scanTime + holdTime;
                
                App.AlarmService.Debug("线扫计算", 
                    $"线扫持续时间计算 - 电压范围: {voltageRange:F3}V, " +
                    $"扫描时间: {scanTime:F1}秒, 保持时间: {holdTime:F1}秒, " +
                    $"总时间: {totalDuration:F1}秒");

                return totalDuration;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("线扫计算", "计算线扫持续时间失败", ex);
                return 0;
            }
        }

        /// <summary>
        /// 检查系统故障条件。
        /// 返回：是否检测到故障和故障原因。
        /// </summary>
        private bool CheckSystemFaultConditions(DataPoint dataPoint, out string stopReason)
        {
            stopReason = string.Empty;

            try
            {
                // 获取当前模式的安全参数
                var safetyParams = GetCurrentModeSafetyParameters();
                if (safetyParams == null)
                {
                    App.AlarmService.Warning("安全检查", "无法获取当前模式的安全参数，使用默认值");
                    safetyParams = new ExperimentSafetyParameters(); // 使用默认值
                }

                // 检查温度过高
                if (dataPoint.Temperature > (decimal)safetyParams.TemperatureThreshold)
                {
                    stopReason = $"温度过高警告: {dataPoint.Temperature:F1}°C，已超过{safetyParams.TemperatureThreshold:F1}°C安全限制";
                    return true;
                }

                // 检查电压异常
                if (dataPoint.Voltage > (decimal)safetyParams.VoltageUpperLimit || dataPoint.Voltage < (decimal)safetyParams.VoltageLowerLimit)
                {
                    stopReason = $"电压异常: {dataPoint.Voltage:F3}V，超出安全范围({safetyParams.VoltageLowerLimit:F1}V - {safetyParams.VoltageUpperLimit:F1}V)";
                    return true;
                }

                // 检查电流异常
                if (dataPoint.Current > (decimal)safetyParams.CurrentUpperLimit || dataPoint.Current < (decimal)safetyParams.CurrentLowerLimit)
                {
                    stopReason = $"电流异常: {dataPoint.Current:F2}A，超出安全范围({safetyParams.CurrentLowerLimit:F1}A - {safetyParams.CurrentUpperLimit:F1}A)";
                    return true;
                }

                // 检查数据质量
                if (dataPoint.Quality != DataQuality.Normal)
                {
                    App.AlarmService.Warning("数据质量", $"数据质量异常: {dataPoint.Quality}");
                    // 数据质量异常不立即停止，但记录警告
                }

                return false;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("故障检查", "检查系统故障条件失败", ex);
                stopReason = "故障检查异常";
                return true;
            }
        }

        /// <summary>
        /// 获取当前模式的安全参数，从界面控件中读取用户设置的值
        /// </summary>
        /// <returns>当前模式的安全参数对象</returns>
        private ExperimentSafetyParameters? GetCurrentModeSafetyParameters()
        {
            try
            {
                ExperimentSafetyParameters parameters;

                switch (_currentMode)
                {
                    case ExperimentMode.ConstantCurrent:
                        parameters = new ConstantCurrentSafetyParameters();
                        LoadConstantCurrentSafetyParameters(parameters);
                        break;

                    case ExperimentMode.ConstantVoltage:
                        parameters = new ConstantVoltageSafetyParameters();
                        LoadConstantVoltageSafetyParameters(parameters);
                        break;

                    case ExperimentMode.LinearScan:
                        parameters = new LinearScanSafetyParameters();
                        LoadLinearScanSafetyParameters(parameters as LinearScanSafetyParameters);
                        break;

                    default:
                        App.AlarmService.Warning("安全参数", $"未知的实验模式: {_currentMode}");
                        return null;
                }

                return parameters;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("安全参数", "获取当前模式安全参数失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 从界面加载恒流模式安全参数
        /// </summary>
        private void LoadConstantCurrentSafetyParameters(ExperimentSafetyParameters parameters)
        {
            try
            {
                // 温度阈值
                if (FindName("ConstantCurrentTemperatureThresholdBox") is TextBox tempBox &&
                    double.TryParse(tempBox.Text, out double tempThreshold))
                {
                    parameters.TemperatureThreshold = tempThreshold;
                }

                // 电压范围
                if (FindName("ConstantCurrentVoltageMaxBox") is TextBox voltMaxBox &&
                    double.TryParse(voltMaxBox.Text, out double voltMax))
                {
                    parameters.VoltageUpperLimit = voltMax;
                }

                if (FindName("ConstantCurrentVoltageMinBox") is TextBox voltMinBox &&
                    double.TryParse(voltMinBox.Text, out double voltMin))
                {
                    parameters.VoltageLowerLimit = voltMin;
                }

                // 电流范围
                if (FindName("ConstantCurrentCurrentMaxBox") is TextBox currMaxBox &&
                    double.TryParse(currMaxBox.Text, out double currMax))
                {
                    parameters.CurrentUpperLimit = currMax;
                }

                if (FindName("ConstantCurrentCurrentMinBox") is TextBox currMinBox &&
                    double.TryParse(currMinBox.Text, out double currMin))
                {
                    parameters.CurrentLowerLimit = currMin;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("安全参数", "加载恒流模式安全参数失败", ex);
            }
        }

        /// <summary>
        /// 从界面加载恒压模式安全参数
        /// </summary>
        private void LoadConstantVoltageSafetyParameters(ExperimentSafetyParameters parameters)
        {
            try
            {
                // 温度阈值
                if (FindName("ConstantVoltageTemperatureThresholdBox") is TextBox tempBox &&
                    double.TryParse(tempBox.Text, out double tempThreshold))
                {
                    parameters.TemperatureThreshold = tempThreshold;
                }

                // 电压范围
                if (FindName("ConstantVoltageVoltageMaxBox") is TextBox voltMaxBox &&
                    double.TryParse(voltMaxBox.Text, out double voltMax))
                {
                    parameters.VoltageUpperLimit = voltMax;
                }

                if (FindName("ConstantVoltageVoltageMinBox") is TextBox voltMinBox &&
                    double.TryParse(voltMinBox.Text, out double voltMin))
                {
                    parameters.VoltageLowerLimit = voltMin;
                }

                // 电流范围
                if (FindName("ConstantVoltageCurrentMaxBox") is TextBox currMaxBox &&
                    double.TryParse(currMaxBox.Text, out double currMax))
                {
                    parameters.CurrentUpperLimit = currMax;
                }

                if (FindName("ConstantVoltageCurrentMinBox") is TextBox currMinBox &&
                    double.TryParse(currMinBox.Text, out double currMin))
                {
                    parameters.CurrentLowerLimit = currMin;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("安全参数", "加载恒压模式安全参数失败", ex);
            }
        }

        /// <summary>
        /// 从界面加载线扫模式安全参数
        /// </summary>
        private void LoadLinearScanSafetyParameters(LinearScanSafetyParameters? parameters)
        {
            try
            {
                if (parameters == null) return;

                // 温度阈值
                if (FindName("LinearScanTemperatureThresholdBox") is TextBox tempBox &&
                    double.TryParse(tempBox.Text, out double tempThreshold))
                {
                    parameters.TemperatureThreshold = tempThreshold;
                }

                // 电压范围
                if (FindName("LinearScanVoltageMaxBox") is TextBox voltMaxBox &&
                    double.TryParse(voltMaxBox.Text, out double voltMax))
                {
                    parameters.VoltageUpperLimit = voltMax;
                }

                if (FindName("LinearScanVoltageMinBox") is TextBox voltMinBox &&
                    double.TryParse(voltMinBox.Text, out double voltMin))
                {
                    parameters.VoltageLowerLimit = voltMin;
                }

                // 电流范围
                if (FindName("LinearScanCurrentMaxBox") is TextBox currMaxBox &&
                    double.TryParse(currMaxBox.Text, out double currMax))
                {
                    parameters.CurrentUpperLimit = currMax;
                }

                if (FindName("LinearScanCurrentMinBox") is TextBox currMinBox &&
                    double.TryParse(currMinBox.Text, out double currMin))
                {
                    parameters.CurrentLowerLimit = currMin;
                }

                // 线扫特有参数
                if (FindName("LinearScanMaxRateBox") is TextBox maxRateBox &&
                    double.TryParse(maxRateBox.Text, out double maxRate))
                {
                    parameters.MaxScanRate = maxRate;
                }

                if (FindName("LinearScanDefaultDurationBox") is TextBox defaultDurationBox &&
                    double.TryParse(defaultDurationBox.Text, out double defaultDuration))
                {
                    parameters.DefaultScanDuration = defaultDuration;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("安全参数", "加载线扫模式安全参数失败", ex);
            }
        }

        // 修复 ScanMethod_Checked 方法
        /// <summary>
        /// 线性扫描模式中“按速率/按时间”单选按钮切换事件处理器。
        /// 触发条件：用户在扫描方式单选按钮之间切换。
        /// 处理逻辑：在“扫描速率”与“扫描时间”两个参数组之间切换可见性。
        /// 对 UI 的影响：互斥显示相应参数输入区域。
        /// </summary>
        private void ScanMethod_Checked(object sender, RoutedEventArgs e)
        {
            var scanRateGroup = FindName("ScanRateGroup") as Grid;
            var scanTimeGroup = FindName("ScanTimeGroup") as Grid;
            var scanByRateRadio = FindName("ScanByRateRadio") as RadioButton;

            if (scanRateGroup == null || scanTimeGroup == null || scanByRateRadio == null) return;

            if (scanByRateRadio.IsChecked == true)
            {
                scanRateGroup.Visibility = Visibility.Visible;
                scanTimeGroup.Visibility = Visibility.Collapsed;
            }
            else
            {
                scanRateGroup.Visibility = Visibility.Collapsed;
                scanTimeGroup.Visibility = Visibility.Visible;
            }
        }

        /// <summary>
        /// 导出当前曲线数据的菜单项/按钮点击事件处理器。
        /// 说明：当前为占位实现，后续将导出为文件。
        /// </summary>
        private void ExportChart_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("导出当前波形数据功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 显示历史记录窗口的菜单项/按钮点击事件处理器。
        /// 说明：当前为占位实现，后续将打开历史记录界面。
        /// </summary>
        private void ShowHistory_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("历史记录窗口功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 不限时选项变更事件：勾选或取消时禁用/启用持续时间输入，并更新徽标
        /// </summary>
        private void UnlimitedDuration_Checked(object sender, RoutedEventArgs e)
        {
            UpdateUnlimitedUIState();
        }
        private void UnlimitedDuration_Unchecked(object sender, RoutedEventArgs e)
        {
            UpdateUnlimitedUIState();
        }

        /// <summary>
        /// 根据当前模式与复选框状态，更新持续时间输入框的可用性和“无限制时间”徽标可见性
        /// </summary>
        private void UpdateUnlimitedUIState()
        {
            try
            {
                bool isUnlimited = false;
                switch (_currentMode)
                {
                    case ExperimentMode.ConstantCurrent:
                        isUnlimited = (FindName("ConstantCurrentUnlimitedCheckBox") as CheckBox)?.IsChecked == true;
                        if (FindName("ConstantCurrentDurationBox") is TextBox ccDuration)
                        {
                            ccDuration.IsEnabled = !isUnlimited;
                        }
                        break;
                    case ExperimentMode.ConstantVoltage:
                        isUnlimited = (FindName("ConstantVoltageUnlimitedCheckBox") as CheckBox)?.IsChecked == true;
                        if (FindName("ConstantVoltageDurationBox") is TextBox cvDuration)
                        {
                            cvDuration.IsEnabled = !isUnlimited;
                        }
                        break;
                }

                if (FindName("UnlimitedBadge") is TextBlock badge)
                {
                    badge.Visibility = isUnlimited ? Visibility.Visible : Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("界面更新", "更新不限时UI状态失败", ex);
            }
        }

        /// <summary>
        /// 设备设置按钮的统一事件处理器。
        /// 触发条件：
        /// - 设备列表中某个“设置”按钮（Tag 为设备名）被点击；
        /// - 顶部通用“设备设置”按钮被点击。
        /// 处理逻辑：根据来源决定打开特定设备设置或通用设置窗口；保存成功后刷新设备状态。
        /// 对 UI 的影响：弹出设置对话框并可能更新设备状态显示。
        /// </summary>
        private void DeviceSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Debug("设备设置", "DeviceSettingsButton_Click 事件被触发");

                DeviceSettingsWindow deviceSettingsWindow;

                // 检查是否是来自设备列表中的设置按钮
                if (sender is Button button && button.Tag is string deviceName)
                {
                    // 来自设备列表的设置按钮，打开特定设备的设置
                    App.AlarmService.Info("设备设置", $"打开设备 {deviceName} 的设置窗口");
                    deviceSettingsWindow = new DeviceSettingsWindow(deviceName);
                }
                else
                {
                    // 来自通用设置按钮，打开所有设备设置
                    App.AlarmService.Info("设备设置", "打开通用设备设置窗口");
                    deviceSettingsWindow = new DeviceSettingsWindow();
                }

                deviceSettingsWindow.Owner = this; // 设置父窗口

                // 显示为模态对话框
                bool? result = deviceSettingsWindow.ShowDialog();

                if (result == true)
                {
                    // 用户点击了保存按钮，可以在这里处理保存后的逻辑
                    // 比如重新加载设备状态、刷新连接状态等
                    RefreshDeviceStatus();
                    App.AlarmService.Info("设备设置", "设备设置保存完成，刷新设备状态");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开设备设置窗口失败", ex);
                MessageBox.Show($"打开设备设置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 重新检测并刷新设备连接状态。
        /// 功能：根据设备的使能状态模拟连接结果，并通知界面更新。
        /// 说明：当前为模拟实现，后续应替换为真实的连接检测逻辑。
        /// </summary>
        private void RefreshDeviceStatus()
        {
            // 重新检查设备连接状态
            // TODO: 根据保存的设备设置重新测试连接状态

            // 这里可以添加实际的设备状态检查逻辑
            // 现在先使用简单的模拟刷新
            foreach (var device in Devices)
            {
                // 模拟重新检测设备状态
                if (device.IsEnabled)
                {
                    // 这里应该根据实际的设备设置进行连接测试
                    // 暂时使用随机模拟
                    var random = new Random();
                    device.IsConnected = random.NextDouble() > 0.2; // 80%概率连接成功
                }
                else
                {
                    device.IsConnected = false;
                }
            }

            // 通知UI更新（由于使用了INotifyPropertyChanged，这一步可能不必要，但保险起见）
            OnPropertyChanged(nameof(Devices));
        }

        /// <summary>
        /// 窗口关闭事件覆盖实现。
        /// 主要步骤：
        /// 1) 标记应用即将关闭；
        /// 2) 停止数据采集与定时器；
        /// 3) 记录关闭日志并安全处理异常。
        /// </summary>
        /// <param name="e">关闭事件参数。</param>
        protected override async void OnClosed(EventArgs e)
        {
            try
            {
                // 设置应用程序关闭标志
                App.AlarmService?.SetApplicationShuttingDown();

                // 停止数据采集
                if (_dataAcquisitionService != null)
                {
                    await _dataAcquisitionService.StopAcquisitionAsync();
                }

                // 停止定时器
                _dataTimer?.Stop();

                App.AlarmService?.Info("主窗口", "主窗口已关闭");
            }
            catch (Exception ex)
            {
                // 在窗口关闭时，使用安全的方式记录异常
                try
                {
                    App.AlarmService?.Error("主窗口", "关闭窗口时发生异常", ex);
                }
                catch
                {
                    // 如果 AlarmService 也失败了，至少记录到控制台
                    System.Console.WriteLine($"主窗口关闭时发生异常: {ex.Message}");
                }
            }
            finally
            {
                base.OnClosed(e);
            }
        }

        #region INotifyPropertyChanged Implementation
        /// <summary>
        /// 属性变更事件。用于通知界面数据绑定更新。
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;
        /// <summary>
        /// 触发 <see cref="PropertyChanged"/> 事件。
        /// </summary>
        /// <param name="propertyName">发生变更的属性名称。</param>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion

        #region 设备状态面板事件处理

        // 电源设置按钮
        /// <summary>
        /// 电源设备“设置”按钮点击事件处理器。
        /// 触发条件：用户点击电源行的设置按钮。
        /// 处理逻辑：构造带设备名的模拟按钮，复用通用设置处理逻辑打开“直流电源”的设置窗口。
        /// 对 UI 的影响：弹出设备设置对话框。
        /// </summary>
        private void PowerSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开电源设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "直流电源" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开电源设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 温控器禁用按钮
        /// <summary>
        /// 温控器“禁用/启用”按钮点击事件处理器（占位）。
        /// 触发条件：用户点击温控器禁用/启用按钮。
        /// 处理逻辑：后续对接实际设备控制接口。
        /// 对 UI 的影响：当前仅弹出提示。
        /// </summary>
        private void TemperatureDisableButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备控制", "温控器禁用/启用操作");
                // TODO: 实现温控器禁用/启用逻辑
                MessageBox.Show("温控器禁用/启用功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备控制", "温控器禁用/启用失败", ex);
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 温控器设置按钮
        /// <summary>
        /// 温控器“设置”按钮点击事件处理器。
        /// 触发条件：用户点击温控器行的设置按钮。
        /// 处理逻辑：构造带设备名的模拟按钮，复用通用设置处理逻辑打开“温控仪”的设置窗口。
        /// 对 UI 的影响：弹出设备设置对话框。
        /// </summary>
        private void TemperatureSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开温控器设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "温控仪" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开温控器设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 流量泵1禁用按钮
        /// <summary>
        /// 流量泵1“禁用/启用”按钮点击事件处理器（占位）。
        /// 触发条件：用户点击流量泵1禁用/启用按钮。
        /// 处理逻辑：后续接入实际设备控制。
        /// 对 UI 的影响：当前仅弹出提示。
        /// </summary>
        private void FlowPump1DisableButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备控制", "流量泵1禁用/启用操作");
                // TODO: 实现流量泵1禁用/启用逻辑
                MessageBox.Show("流量泵1禁用/启用功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备控制", "流量泵1禁用/启用失败", ex);
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 流量泵1设置按钮
        /// <summary>
        /// 流量泵1“设置”按钮点击事件处理器。
        /// 触发条件：用户点击流量泵1行的设置按钮。
        /// 处理逻辑：构造带设备名的模拟按钮，复用通用设置处理逻辑打开“流量泵 1”的设置窗口。
        /// 对 UI 的影响：弹出设备设置对话框。
        /// </summary>
        private void FlowPump1SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开流量泵1设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "流量泵 1" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开流量泵1设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 流量泵2禁用按钮
        /// <summary>
        /// 流量泵2“禁用/启用”按钮点击事件处理器（占位）。
        /// 触发条件：用户点击流量泵2禁用/启用按钮。
        /// 处理逻辑：后续接入实际设备控制。
        /// 对 UI 的影响：当前仅弹出提示。
        /// </summary>
        private void FlowPump2DisableButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备控制", "流量泵2禁用/启用操作");
                // TODO: 实现流量泵2禁用/启用逻辑
                MessageBox.Show("流量泵2禁用/启用功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备控制", "流量泵2禁用/启用失败", ex);
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 流量泵2设置按钮
        /// <summary>
        /// 流量泵2“设置”按钮点击事件处理器。
        /// 触发条件：用户点击流量泵2行的设置按钮。
        /// 处理逻辑：构造带设备名的模拟按钮，复用通用设置处理逻辑打开“流量泵 2”的设置窗口。
        /// 对 UI 的影响：弹出设备设置对话框。
        /// </summary>
        private void FlowPump2SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开流量泵2设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "流量泵 2" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开流量泵2设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 数据库设置按钮
        /// <summary>
        /// 数据库“设置”按钮点击事件处理器。
        /// 触发条件：用户点击数据库行的设置按钮。
        /// 处理逻辑：构造带设备名的模拟按钮，复用通用设置处理逻辑打开“数据库”的设置窗口。
        /// 对 UI 的影响：弹出设备设置对话框。
        /// </summary>
        private void DatabaseSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开数据库设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "数据库" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开数据库设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region 实验参数设置事件处理

        // 配置文件选择按钮
        /// <summary>
        /// “选择配置文件”按钮点击事件处理器。
        /// 触发条件：用户选择要加载的 JSON 配置文件。
        /// 处理逻辑：打开文件对话框并调用加载方法。
        /// 对 UI 的影响：应用配置到界面相关控件。
        /// </summary>
        private void SelectConfigButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 确保参数配置目录存在
                var paramDir = GetParameterConfigDirectory();
                Directory.CreateDirectory(paramDir);

                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "选择实验配置文件",
                    Filter = "JSON 配置文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = ".json",
                    InitialDirectory = paramDir
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    LoadExperimentConfiguration(openFileDialog.FileName);
                    App.AlarmService.Info("配置管理", $"加载配置文件: {openFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "选择配置文件失败", ex);
                MessageBox.Show($"选择配置文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 配置文件保存按钮
        /// <summary>
        /// “保存配置文件”按钮点击事件处理器。
        /// 触发条件：用户希望将当前界面参数保存为 JSON。
        /// 处理逻辑：打开另存为对话框，收集参数并写入文件。
        /// 对 UI 的影响：显示保存成功或错误提示。
        /// </summary>
        private void SaveConfigButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 确保参数配置目录存在
                var paramDir = GetParameterConfigDirectory();
                Directory.CreateDirectory(paramDir);

                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "保存实验配置文件",
                    Filter = "JSON 配置文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = ".json",
                    InitialDirectory = paramDir,
                    FileName = "新实验配置.json"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    SaveExperimentConfiguration(saveFileDialog.FileName);
                    App.AlarmService.Info("配置管理", $"保存配置文件: {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "保存配置文件失败", ex);
                MessageBox.Show($"保存配置文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从指定路径加载实验配置（JSON）。
        /// 参数：filePath 配置文件完整路径。
        /// 返回：无。
        /// 异常：内部捕获并提示错误信息。
        /// </summary>
        private void LoadExperimentConfiguration(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    MessageBox.Show($"配置文件不存在：{filePath}", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    UpdateConfigFileNameDisplay("(未找到)");
                    return;
                }

                var json = File.ReadAllText(filePath);
                var root = JObject.Parse(json);

                ApplyConfigurationToUI(root);

                // 更新配置文件名称显示
                UpdateConfigFileNameDisplay(System.IO.Path.GetFileName(filePath));

                App.AlarmService.Info("配置管理", $"已加载配置：{filePath}");
            }
            catch (Exception ex)
            {
                // 配置加载失败时更新显示
                UpdateConfigFileNameDisplay("(加载失败)");
                App.AlarmService.Error("配置管理", "加载配置文件失败", ex);
                //MessageBox.Show($"加载配置文件失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 将当前模式下的实验参数保存为 JSON 文件。
        /// 参数：filePath 保存文件的完整路径。
        /// 返回：无。
        /// 异常：内部捕获并提示错误信息。
        /// </summary>
        private void SaveExperimentConfiguration(string filePath)
        {
            try
            {
                var root = CollectConfigurationForCurrentMode();

                var dir = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(dir)) Directory.CreateDirectory(dir);

                File.WriteAllText(filePath, root.ToString(Formatting.Indented));
                App.AlarmService.Info("配置管理", $"已保存配置：{filePath}");
                //MessageBox.Show("配置保存成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "保存配置文件失败", ex);
                //MessageBox.Show($"保存配置文件失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        /// <summary>
        /// 获取参数配置文件的默认目录。
        /// 说明：当前固定为应用目录下的“Configs”文件夹，可扩展为从配置读取。
        /// 返回：目录的绝对路径。
        /// </summary>
        private string GetParameterConfigDirectory()
        {
            try
            {
                // 如需改为配置项：可结合 ConfigurationService 获取自定义目录名
                // var folderName = ((IConfigurationService)App.Host.Services.GetService(typeof(IConfigurationService)))?.GetStringSetting("ParameterConfigFolder", "Configs") ?? "Configs";
                var folderName = "Configs";
                return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, folderName);
            }
            catch
            {
                return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs");
            }
        }

        /// <summary>
        /// 根据当前实验模式收集界面上的参数，构建 JSON 对象。
        /// 包含通用参数与模式特有参数，数值将按 InvariantCulture 解析。
        /// 返回：JObject 结构的配置内容。
        /// </summary>
        private JObject CollectConfigurationForCurrentMode()
        {
            // 通用参数（各模式都有一个通用区块；XAML中每个模式的“通用参数”TextBox命名不同，这里按当前模式收集）
            double ParseD(string s)
            {
                if (string.IsNullOrWhiteSpace(s)) return 0d;
                double v;
                if (double.TryParse(s, NumberStyles.Any, CultureInfo.InvariantCulture, out v) ||
                    double.TryParse(s, NumberStyles.Any, CultureInfo.CurrentCulture, out v))
                {
                    return v;
                }
                App.AlarmService?.Warning("参数解析", $"无法解析为 double：'{s}'，已使用默认值 0");
                return 0d;
            }
            int ParseI(string s)
            {
                if (string.IsNullOrWhiteSpace(s)) return 0;
                int v;
                if (int.TryParse(s, NumberStyles.Any, CultureInfo.InvariantCulture, out v) ||
                    int.TryParse(s, NumberStyles.Any, CultureInfo.CurrentCulture, out v))
                {
                    return v;
                }
                App.AlarmService?.Warning("参数解析", $"无法解析为 int：'{s}'，已使用默认值 0");
                return 0;
            }

            JObject root = new JObject();

            switch (_currentMode)
            {
                case ExperimentMode.ConstantCurrent:
                    root["ExperimentType"] = "ConstantCurrent";
                    root["Common"] = new JObject
                    {
                        ["TargetTemperature"] = ParseD((FindName("ConstantCurrentTargetTemperatureBox") as TextBox)?.Text ?? "0"),
                        ["FlowRate1"] = ParseD((FindName("ConstantCurrentFlowPump1Box") as TextBox)?.Text ?? "0"),
                        ["FlowRate2"] = ParseD((FindName("ConstantCurrentFlowPump2Box") as TextBox)?.Text ?? "0"),
                        ["RepeatCount"] = ParseI((FindName("ConstantCurrentRepeatCountBox") as TextBox)?.Text ?? "1"),
                        ["SamplingInterval"] = ParseD((FindName("ConstantCurrentSamplingIntervalBox") as TextBox)?.Text ?? "1")
                    };
                    root["ConstantCurrent"] = new JObject
                    {
                        ["TargetCurrent"] = ParseD((FindName("ConstantCurrentTargetCurrentBox") as TextBox)?.Text ?? "0"),
                        ["Duration"] = ParseI((FindName("ConstantCurrentDurationBox") as TextBox)?.Text ?? "0"),
                        ["UnlimitedDuration"] = (FindName("ConstantCurrentUnlimitedCheckBox") as CheckBox)?.IsChecked ?? false,
                        ["VoltageUpperLimit"] = ParseD((FindName("ConstantCurrentVoltageUpperLimitBox") as TextBox)?.Text ?? "0"),
                        ["VoltageLowerLimit"] = ParseD((FindName("ConstantCurrentVoltageLowerLimitBox") as TextBox)?.Text ?? "0")
                    };
                    // 添加安全参数
                    root["SafetyParameters"] = new JObject
                    {
                        ["TemperatureThreshold"] = ParseD((FindName("ConstantCurrentTemperatureThresholdBox") as TextBox)?.Text ?? "95.0"),
                        ["VoltageMax"] = ParseD((FindName("ConstantCurrentVoltageMaxBox") as TextBox)?.Text ?? "10.0"),
                        ["VoltageMin"] = ParseD((FindName("ConstantCurrentVoltageMinBox") as TextBox)?.Text ?? "0.0"),
                        ["CurrentMax"] = ParseD((FindName("ConstantCurrentCurrentMaxBox") as TextBox)?.Text ?? "200.0"),
                        ["CurrentMin"] = ParseD((FindName("ConstantCurrentCurrentMinBox") as TextBox)?.Text ?? "0.0")
                    };
                    break;

                case ExperimentMode.ConstantVoltage:
                    root["ExperimentType"] = "ConstantVoltage";
                    root["Common"] = new JObject
                    {
                        ["TargetTemperature"] = ParseD((FindName("ConstantVoltageTargetTemperatureBox") as TextBox)?.Text ?? "0"),
                        ["FlowRate1"] = ParseD((FindName("ConstantVoltageFlowPump1Box") as TextBox)?.Text ?? "0"),
                        ["FlowRate2"] = ParseD((FindName("ConstantVoltageFlowPump2Box") as TextBox)?.Text ?? "0"),
                        ["RepeatCount"] = ParseI((FindName("ConstantVoltageRepeatCountBox") as TextBox)?.Text ?? "1"),
                        ["SamplingInterval"] = ParseD((FindName("ConstantVoltageSamplingIntervalBox") as TextBox)?.Text ?? "1")
                    };
                    root["ConstantVoltage"] = new JObject
                    {
                        ["TargetVoltage"] = ParseD((FindName("ConstantVoltageTargetVoltageBox") as TextBox)?.Text ?? "0"),
                        ["Duration"] = ParseI((FindName("ConstantVoltageDurationBox") as TextBox)?.Text ?? "0"),
                        ["UnlimitedDuration"] = (FindName("ConstantVoltageUnlimitedCheckBox") as CheckBox)?.IsChecked ?? false,
                        ["CurrentUpperLimit"] = ParseD((FindName("ConstantVoltageCurrentUpperLimitBox") as TextBox)?.Text ?? "0"),
                        ["CurrentLowerLimit"] = ParseD((FindName("ConstantVoltageCurrentLowerLimitBox") as TextBox)?.Text ?? "0")
                    };
                    // 添加安全参数
                    root["SafetyParameters"] = new JObject
                    {
                        ["TemperatureThreshold"] = ParseD((FindName("ConstantVoltageTemperatureThresholdBox") as TextBox)?.Text ?? "95.0"),
                        ["VoltageMax"] = ParseD((FindName("ConstantVoltageVoltageMaxBox") as TextBox)?.Text ?? "10.0"),
                        ["VoltageMin"] = ParseD((FindName("ConstantVoltageVoltageMinBox") as TextBox)?.Text ?? "0.0"),
                        ["CurrentMax"] = ParseD((FindName("ConstantVoltageCurrentMaxBox") as TextBox)?.Text ?? "200.0"),
                        ["CurrentMin"] = ParseD((FindName("ConstantVoltageCurrentMinBox") as TextBox)?.Text ?? "0.0")
                    };
                    break;

                case ExperimentMode.LinearScan:
                    root["ExperimentType"] = "LinearScan";
                    root["Common"] = new JObject
                    {
                        ["TargetTemperature"] = ParseD((FindName("LinearScanTargetTemperatureBox") as TextBox)?.Text ?? "0"),
                        ["FlowRate1"] = ParseD((FindName("LinearScanFlowPump1Box") as TextBox)?.Text ?? "0"),
                        ["FlowRate2"] = ParseD((FindName("LinearScanFlowPump2Box") as TextBox)?.Text ?? "0"),
                        ["RepeatCount"] = ParseI((FindName("LinearScanRepeatCountBox") as TextBox)?.Text ?? "1"),
                        ["SamplingInterval"] = ParseD((FindName("LinearScanSamplingIntervalBox") as TextBox)?.Text ?? "1")
                    };
                    var scanByRate = (FindName("ScanByRateRadio") as RadioButton)?.IsChecked == true;
                    root["LinearScan"] = new JObject
                    {
                        ["StartVoltage"] = ParseD((FindName("LinearScanStartVoltageBox") as TextBox)?.Text ?? "0"),
                        ["EndVoltage"] = ParseD((FindName("LinearScanEndVoltageBox") as TextBox)?.Text ?? "0"),
                        ["ScanByRate"] = scanByRate,
                        ["Rate"] = scanByRate ? ParseD((FindName("LinearScanRateBox") as TextBox)?.Text ?? "0") : 0.0,
                        ["Time"] = !scanByRate ? ParseI((FindName("LinearScanTimeBox") as TextBox)?.Text ?? "0") : 0,
                        ["CurrentUpperLimit"] = ParseD((FindName("LinearScanCurrentUpperLimitBox") as TextBox)?.Text ?? "0"),
                        ["CurrentLowerLimit"] = ParseD((FindName("LinearScanCurrentLowerLimitBox") as TextBox)?.Text ?? "0"),
                        ["HoldTime"] = ParseI((FindName("LinearScanHoldTimeBox") as TextBox)?.Text ?? "0")
                    };
                    // 添加安全参数
                    root["SafetyParameters"] = new JObject
                    {
                        ["TemperatureThreshold"] = ParseD((FindName("LinearScanTemperatureThresholdBox") as TextBox)?.Text ?? "95.0"),
                        ["VoltageMax"] = ParseD((FindName("LinearScanVoltageMaxBox") as TextBox)?.Text ?? "10.0"),
                        ["VoltageMin"] = ParseD((FindName("LinearScanVoltageMinBox") as TextBox)?.Text ?? "0.0"),
                        ["CurrentMax"] = ParseD((FindName("LinearScanCurrentMaxBox") as TextBox)?.Text ?? "200.0"),
                        ["CurrentMin"] = ParseD((FindName("LinearScanCurrentMinBox") as TextBox)?.Text ?? "0.0"),
                        ["MaxScanRate"] = ParseD((FindName("LinearScanMaxRateBox") as TextBox)?.Text ?? "1.0"),
                        ["DefaultScanDuration"] = ParseD((FindName("LinearScanDefaultDurationBox") as TextBox)?.Text ?? "120.0")
                    };
                    break;
            }

            return root;
        }

        /// <summary>
        /// 将 JSON 配置值应用到界面控件。
        /// 功能：按 ExperimentType 切换与填充对应模式的输入框，并处理扫描方式切换联动。
        /// </summary>
        private void ApplyConfigurationToUI(JObject root)
        {
            try
            {
                // 根据 ExperimentType 切换到对应 Tab，并在填充参数前完成切换
                var type = (string?)root["ExperimentType"] ?? string.Empty;
                try
                {
                    var expectedTag = type == "LinearVoltageRamp" ? "LinearScan" : type;
                    var tabControl = FindName("ExperimentModeTabControl") as TabControl;
                    if (tabControl == null)
                    {
                        App.AlarmService?.Warning("配置管理", "未找到 TabControl：ExperimentModeTabControl，无法切换到指定实验模式");
                    }
                    else if (!string.IsNullOrWhiteSpace(expectedTag))
                    {
                        TabItem? targetTab = null;
                        foreach (var item in tabControl.Items)
                        {
                            if (item is TabItem ti && string.Equals(Convert.ToString(ti.Tag), expectedTag, StringComparison.Ordinal))
                            {
                                targetTab = ti;
                                break;
                            }
                        }
                        if (targetTab != null)
                        {
                            tabControl.SelectedItem = targetTab;
                            tabControl.UpdateLayout();
                        }
                        else
                        {
                            App.AlarmService?.Warning("配置管理", $"未找到与 ExperimentType='{type}' 对应的 TabItem（Tag='{expectedTag}'）");
                        }
                    }
                    else
                    {
                        App.AlarmService?.Warning("配置管理", "配置中缺少有效的 ExperimentType，跳过 Tab 切换");
                    }
                }
                catch (Exception ex)
                {
                    App.AlarmService?.Error("配置管理", "切换实验模式 Tab 时发生异常，将继续填充参数", ex);
                }
                if (type == "ConstantCurrent")
                {
                    SetIfPresent("ConstantCurrentTargetTemperatureBox", root["Common"]?["TargetTemperature"]);
                    SetIfPresent("ConstantCurrentFlowPump1Box", root["Common"]?["FlowRate1"]);
                    SetIfPresent("ConstantCurrentFlowPump2Box", root["Common"]?["FlowRate2"]);
                    SetIfPresent("ConstantCurrentRepeatCountBox", root["Common"]?["RepeatCount"]);
                    SetIfPresent("ConstantCurrentSamplingIntervalBox", root["Common"]?["SamplingInterval"]);

                    SetIfPresent("ConstantCurrentTargetCurrentBox", root["ConstantCurrent"]?["TargetCurrent"]);
                    SetIfPresent("ConstantCurrentDurationBox", root["ConstantCurrent"]?["Duration"]);
                    // 不限时复选框
                    if (FindName("ConstantCurrentUnlimitedCheckBox") is CheckBox ccUnlimited)
                    {
                        ccUnlimited.IsChecked = (bool?)(root["ConstantCurrent"]?["UnlimitedDuration"]) ?? false;
                    }
                    SetIfPresent("ConstantCurrentVoltageUpperLimitBox", root["ConstantCurrent"]?["VoltageUpperLimit"]);
                    SetIfPresent("ConstantCurrentVoltageLowerLimitBox", root["ConstantCurrent"]?["VoltageLowerLimit"]);

                    // 加载安全参数
                    SetIfPresent("ConstantCurrentTemperatureThresholdBox", root["SafetyParameters"]?["TemperatureThreshold"]);
                    SetIfPresent("ConstantCurrentVoltageMaxBox", root["SafetyParameters"]?["VoltageMax"]);
                    SetIfPresent("ConstantCurrentVoltageMinBox", root["SafetyParameters"]?["VoltageMin"]);
                    SetIfPresent("ConstantCurrentCurrentMaxBox", root["SafetyParameters"]?["CurrentMax"]);
                    SetIfPresent("ConstantCurrentCurrentMinBox", root["SafetyParameters"]?["CurrentMin"]);

                    UpdateUnlimitedUIState();
                }
                else if (type == "ConstantVoltage")
                {
                    SetIfPresent("ConstantVoltageTargetTemperatureBox", root["Common"]?["TargetTemperature"]);
                    SetIfPresent("ConstantVoltageFlowPump1Box", root["Common"]?["FlowRate1"]);
                    SetIfPresent("ConstantVoltageFlowPump2Box", root["Common"]?["FlowRate2"]);
                    SetIfPresent("ConstantVoltageRepeatCountBox", root["Common"]?["RepeatCount"]);
                    SetIfPresent("ConstantVoltageSamplingIntervalBox", root["Common"]?["SamplingInterval"]);

                    SetIfPresent("ConstantVoltageTargetVoltageBox", root["ConstantVoltage"]?["TargetVoltage"]);
                    SetIfPresent("ConstantVoltageDurationBox", root["ConstantVoltage"]?["Duration"]);
                    // 不限时复选框
                    if (FindName("ConstantVoltageUnlimitedCheckBox") is CheckBox cvUnlimited)
                    {
                        cvUnlimited.IsChecked = (bool?)(root["ConstantVoltage"]?["UnlimitedDuration"]) ?? false;
                    }
                    SetIfPresent("ConstantVoltageCurrentUpperLimitBox", root["ConstantVoltage"]?["CurrentUpperLimit"]);
                    SetIfPresent("ConstantVoltageCurrentLowerLimitBox", root["ConstantVoltage"]?["CurrentLowerLimit"]);

                    // 加载安全参数
                    SetIfPresent("ConstantVoltageTemperatureThresholdBox", root["SafetyParameters"]?["TemperatureThreshold"]);
                    SetIfPresent("ConstantVoltageVoltageMaxBox", root["SafetyParameters"]?["VoltageMax"]);
                    SetIfPresent("ConstantVoltageVoltageMinBox", root["SafetyParameters"]?["VoltageMin"]);
                    SetIfPresent("ConstantVoltageCurrentMaxBox", root["SafetyParameters"]?["CurrentMax"]);
                    SetIfPresent("ConstantVoltageCurrentMinBox", root["SafetyParameters"]?["CurrentMin"]);

                    UpdateUnlimitedUIState();
                }
                else if (type == "LinearScan" || type == "LinearVoltageRamp")
                {
                    SetIfPresent("LinearScanTargetTemperatureBox", root["Common"]?["TargetTemperature"]);
                    SetIfPresent("LinearScanFlowPump1Box", root["Common"]?["FlowRate1"]);
                    SetIfPresent("LinearScanFlowPump2Box", root["Common"]?["FlowRate2"]);
                    SetIfPresent("LinearScanRepeatCountBox", root["Common"]?["RepeatCount"]);
                    SetIfPresent("LinearScanSamplingIntervalBox", root["Common"]?["SamplingInterval"]);

                    var linear = root["LinearScan"] ?? root["LinearVoltageRamp"];
                    SetIfPresent("LinearScanStartVoltageBox", linear?["StartVoltage"]);
                    SetIfPresent("LinearScanEndVoltageBox", linear?["EndVoltage"]);

                    var scanByRate = (bool?)(linear?["ScanByRate"]) ?? true;
                    var scanByRateRadio = FindName("ScanByRateRadio") as RadioButton;
                    var scanTimeRadio = FindName("ScanByTimeRadio") as RadioButton;
                    if (scanByRateRadio != null && scanTimeRadio != null)
                    {
                        scanByRateRadio.IsChecked = scanByRate;
                        scanTimeRadio.IsChecked = !scanByRate;
                        ScanMethod_Checked(scanByRateRadio, new RoutedEventArgs());
                    }
                    if (scanByRate)
                        SetIfPresent("LinearScanRateBox", linear?["Rate"]);
                    else
                        SetIfPresent("LinearScanTimeBox", linear?["Time"]);

                    SetIfPresent("LinearScanCurrentUpperLimitBox", linear?["CurrentUpperLimit"]);
                    SetIfPresent("LinearScanCurrentLowerLimitBox", linear?["CurrentLowerLimit"]);
                    SetIfPresent("LinearScanHoldTimeBox", linear?["HoldTime"]);

                    // 加载安全参数
                    SetIfPresent("LinearScanTemperatureThresholdBox", root["SafetyParameters"]?["TemperatureThreshold"]);
                    SetIfPresent("LinearScanVoltageMaxBox", root["SafetyParameters"]?["VoltageMax"]);
                    SetIfPresent("LinearScanVoltageMinBox", root["SafetyParameters"]?["VoltageMin"]);
                    SetIfPresent("LinearScanCurrentMaxBox", root["SafetyParameters"]?["CurrentMax"]);
                    SetIfPresent("LinearScanCurrentMinBox", root["SafetyParameters"]?["CurrentMin"]);
                    SetIfPresent("LinearScanMaxRateBox", root["SafetyParameters"]?["MaxScanRate"]);
                    SetIfPresent("LinearScanDefaultDurationBox", root["SafetyParameters"]?["DefaultScanDuration"]);
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "应用配置到界面失败", ex);
            }
        }

        /// <summary>
        /// 更新界面上“配置文件:”显示的文件名。
        /// 在 UI 线程中安全地设置文本，未找到控件时记录警告。
        /// </summary>
        private void UpdateConfigFileNameDisplay(string? fileName)
        {
            try
            {
                var text = string.IsNullOrWhiteSpace(fileName) ? "配置文件: (未选择)" : $"配置文件: {fileName}";
                if (FindName("ConfigFileNameText") is TextBlock t)
                {
                    if (Dispatcher.CheckAccess())
                        t.Text = text;
                    else
                        Dispatcher.Invoke(() => t.Text = text);
                }
                else
                {
                    App.AlarmService?.Warning("配置管理", "未找到用于显示配置文件名的控件：ConfigFileNameText");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService?.Error("配置管理", "更新配置文件名显示失败", ex);
            }
        }

        /// <summary>
        /// 当配置项存在时，将其值写入指定名称的 TextBox。
        /// 参数：
        /// - textBoxName: TextBox 的名称；
        /// - token: JSON 值，支持数字与字符串。
        /// </summary>
        private void SetIfPresent(string textBoxName, JToken? token)
        {
            if (token == null) return;
            if (FindName(textBoxName) is TextBox tb)
            {
                tb.Text = token.Type == JTokenType.Float || token.Type == JTokenType.Integer
                    ? Convert.ToString((double)token, CultureInfo.InvariantCulture)
                    : token.ToString();
            }
        }


        // 参数验证方法
        /// <summary>
        /// 整体验证实验参数的有效性（占位）。
        /// 返回：true 表示当前参数通过校验；false 表示失败。
        /// 异常：内部捕获并记录，不向外抛出。
        /// </summary>
        private bool ValidateExperimentParameters()
        {
            try
            {
                // TODO: 实现实验参数验证逻辑
                // 验证温度、流量、电流、电压等参数的有效性
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "验证实验参数失败", ex);
                return false;
            }
        }

        // 参数输入框文本变化事件处理
        /// <summary>
        /// 任一参数输入框文本变化时的事件处理器。
        /// 触发条件：用户修改参数输入框内容。
        /// 处理逻辑：调用逐项校验，更新控件边框颜色与提示信息。
        /// </summary>
        private void ParameterTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (sender is TextBox textBox)
                {
                    // 实时验证输入的参数值
                    ValidateParameterInput(textBox);
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "参数输入验证失败", ex);
            }
        }

        /// <summary>
        /// 安全参数输入框文本变化时的事件处理器。
        /// 触发条件：用户修改安全参数输入框内容。
        /// 处理逻辑：使用SafetyParametersManager进行验证，更新控件边框颜色与提示信息。
        /// </summary>
        private void SafetyParameterTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (sender is TextBox textBox)
                {
                    // 实时验证安全参数值
                    ValidateSafetyParameterInput(textBox);
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("安全参数验证", "安全参数输入验证失败", ex);
            }
        }

        /// <summary>
        /// 针对单个安全参数 TextBox 的数值校验与范围提示。
        /// 参数：textBox 需要校验的安全参数输入框；使用SafetyParametersManager进行验证。
        /// </summary>
        private void ValidateSafetyParameterInput(TextBox textBox)
        {
            try
            {
                string parameterName = textBox.Name ?? "未知参数";
                string originalToolTip = textBox.ToolTip?.ToString() ?? "";

                bool isValid = false;
                string errorMessage = "";

                if (double.TryParse(textBox.Text, out double value))
                {
                    // 根据参数名称确定参数类型和实验模式
                    var (paramType, mode) = GetParameterTypeAndMode(parameterName);

                    if (!string.IsNullOrEmpty(paramType))
                    {
                        var validationResult = _safetyParametersManager.ValidateParameter(paramType, value, mode);
                        isValid = validationResult.IsValid;
                        errorMessage = validationResult.ErrorMessage;
                    }
                    else
                    {
                        // 对于无法识别的参数，使用基本范围验证
                        isValid = ValidateBasicSafetyRange(parameterName, value, out errorMessage);
                    }
                }
                else
                {
                    errorMessage = "请输入有效的数值";
                }

                // 更新控件样式
                if (isValid)
                {
                    textBox.BorderBrush = FindResource("BorderColor") as SolidColorBrush;
                    textBox.ToolTip = originalToolTip; // 恢复原始 ToolTip
                }
                else
                {
                    textBox.BorderBrush = Brushes.Red;
                    textBox.ToolTip = errorMessage; // 显示错误信息
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("安全参数验证", $"验证安全参数 {textBox.Name} 失败", ex);
            }
        }

        /// <summary>
        /// 根据参数名称确定参数类型和实验模式
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>参数类型和实验模式</returns>
        private (string ParameterType, Models.System.ExperimentMode Mode) GetParameterTypeAndMode(string parameterName)
        {
            try
            {
                // 确定实验模式
                Models.System.ExperimentMode mode;
                if (parameterName.Contains("ConstantCurrent"))
                {
                    mode = Models.System.ExperimentMode.ConstantCurrent;
                }
                else if (parameterName.Contains("ConstantVoltage"))
                {
                    mode = Models.System.ExperimentMode.ConstantVoltage;
                }
                else if (parameterName.Contains("LinearScan"))
                {
                    mode = Models.System.ExperimentMode.LinearScan;
                }
                else
                {
                    return (string.Empty, Models.System.ExperimentMode.ConstantCurrent);
                }

                // 确定参数类型
                string paramType = string.Empty;
                if (parameterName.Contains("TemperatureThreshold"))
                {
                    paramType = "temperature";
                }
                else if (parameterName.Contains("VoltageMax") || parameterName.Contains("VoltageMin"))
                {
                    paramType = "voltage";
                }
                else if (parameterName.Contains("CurrentMax") || parameterName.Contains("CurrentMin"))
                {
                    paramType = "current";
                }
                else if (parameterName.Contains("MaxRate"))
                {
                    paramType = "scanrate";
                }
                else if (parameterName.Contains("DefaultDuration"))
                {
                    paramType = "duration";
                }

                return (paramType, mode);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数解析", $"解析参数名称 {parameterName} 失败", ex);
                return (string.Empty, Models.System.ExperimentMode.ConstantCurrent);
            }
        }

        /// <summary>
        /// 基本安全范围验证（用于无法识别的参数）
        /// </summary>
        private bool ValidateBasicSafetyRange(string parameterName, double value, out string errorMessage)
        {
            errorMessage = string.Empty;

            try
            {
                // 基本的安全范围检查
                if (parameterName.Contains("Temperature"))
                {
                    if (value < 0 || value > 100)
                    {
                        errorMessage = "温度应在 0-100°C 范围内";
                        return false;
                    }
                }
                else if (parameterName.Contains("Voltage"))
                {
                    if (value < 0 || value > 15)
                    {
                        errorMessage = "电压应在 0-15V 范围内";
                        return false;
                    }
                }
                else if (parameterName.Contains("Current"))
                {
                    if (value < 0 || value > 300)
                    {
                        errorMessage = "电流应在 0-300A 范围内";
                        return false;
                    }
                }
                else if (parameterName.Contains("Rate"))
                {
                    if (value < 0 || value > 10)
                    {
                        errorMessage = "速率应在 0-10 范围内";
                        return false;
                    }
                }
                else if (parameterName.Contains("Duration") || parameterName.Contains("Time"))
                {
                    if (value < 0 || value > 86400)
                    {
                        errorMessage = "时间应在 0-86400 秒范围内";
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("基本验证", $"基本安全范围验证失败: {parameterName}", ex);
                errorMessage = "验证过程中发生错误";
                return false;
            }
        }

        /// <summary>
        /// 针对单个 TextBox 的数值校验与范围提示。
        /// 参数：textBox 需要校验的输入框；依据名称与 ToolTip 判定范围。
        /// </summary>
        private void ValidateParameterInput(TextBox textBox)
        {
            try
            {
                string toolTip = textBox.ToolTip?.ToString() ?? "";
                string parameterName = textBox.Name ?? "未知参数";

                bool isValid = false;
                string errorMessage = "";

                if (double.TryParse(textBox.Text, out double value))
                {
                    // 根据参数名称和 ToolTip 进行范围验证
                    isValid = ValidateParameterRange(parameterName, value, toolTip, out errorMessage);
                }
                else
                {
                    errorMessage = "请输入有效的数值";
                }

                // 更新控件样式
                if (isValid)
                {
                    textBox.BorderBrush = FindResource("BorderColor") as SolidColorBrush;
                    textBox.ToolTip = toolTip; // 恢复原始 ToolTip
                }
                else
                {
                    textBox.BorderBrush = FindResource("DangerColor") as SolidColorBrush;
                    textBox.ToolTip = $"{toolTip}\n错误: {errorMessage}";
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "验证参数输入失败", ex);
            }
        }

        /// <summary>
        /// 根据参数名称判定数值范围是否有效。
        /// 参数：
        /// - parameterName: 控件名称，用于推断参数类型；
        /// - value: 待验证的数值；
        /// - toolTip: 原始提示信息，将在错误时附加说明；
        /// - errorMessage: 输出的错误描述。
        /// 返回：true 表示通过；false 表示未通过。
        /// </summary>
        private bool ValidateParameterRange(string parameterName, double value, string toolTip, out string errorMessage)
        {
            errorMessage = "";

            try
            {
                // 根据参数名称进行特定的范围验证
                // 注意：条件检查顺序很重要，更具体的条件应该优先检查
                // 例如 "ConstantCurrentDurationBox" 包含 "Current" 和 "Duration"，
                // 应该优先匹配 "Duration" 而不是 "Current"

                if (parameterName.Contains("Duration"))
                {
                    // 持续时间验证：范围 1-999999 秒
                    // 注意：如果对应的"无限制"复选框被选中，输入框会被禁用，
                    // 此时 ValidateParameterInput 方法会提前返回，不会执行到这里
                    if (value < 1 || value > 999999)
                    {
                        errorMessage = "持续时间应在 1-999999 秒之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("Temperature"))
                {
                    if (value < 20 || value > 90)
                    {
                        errorMessage = "温度范围应在 20-90°C 之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("FlowPump"))
                {
                    if (value < 0 || value > 400)
                    {
                        errorMessage = "流量范围应在 0-400 mL/min 之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("RepeatCount"))
                {
                    if (value < 1 || value > 999 || value != Math.Floor(value))
                    {
                        errorMessage = "重复次数应为 1-999 的整数";
                        return false;
                    }
                }
                else if (parameterName.Contains("SamplingInterval"))
                {
                    if (value < 0.1 || value > 60)
                    {
                        errorMessage = "采样间隔应在 0.1-60 秒之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("Current"))
                {
                    // 电流验证（放在 Duration 检查之后，避免 "ConstantCurrentDurationBox" 误匹配）
                    if (value < 0 || value > 170)
                    {
                        errorMessage = "电流范围应在 0-170A 之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("Voltage"))
                {
                    // 电压验证（放在 Duration 检查之后，避免 "ConstantVoltageDurationBox" 误匹配）
                    if (value < 0 || value > 10)
                    {
                        errorMessage = "电压范围应在 0-10V 之间";
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", $"验证参数 {parameterName} 范围失败", ex);
                errorMessage = "验证失败";
                return false;
            }
        }

        #endregion
    }

    #region Value Converters - 关键修正：补全所有必需的转换器类

    /// <summary>
    /// 将布尔值转换为删除线效果的转换器：当值为 false 时返回删除线装饰，否则为 null。
    /// 用于在设备禁用时为文本添加删除线视觉提示。
    /// </summary>
    public class BoolToStrikethroughConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值转换为 TextDecorations。false -> 删除线；true -> 无效果。
        /// </summary>
        /// <param name="value">布尔值，表示是否启用。</param>
        /// <param name="targetType">目标类型。</param>
        /// <param name="parameter">可选参数。</param>
        /// <param name="culture">文化信息。</param>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is bool b && !b ? TextDecorations.Strikethrough : null;
        }
        /// <summary>
        /// 不支持反向转换。
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
    }

    /// <summary>
    /// 将布尔值转换为按钮文字的转换器：true -> “禁用”，false -> “启用”。
    /// 用于设备启用状态的按钮内容切换。
    /// </summary>
    public class BoolToButtonContentConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值转换为按钮显示文本。
        /// </summary>
        /// <param name="value">布尔值，表示是否启用。</param>
        /// <param name="targetType">目标类型。</param>
        /// <param name="parameter">可选参数。</param>
        /// <param name="culture">文化信息。</param>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is bool b && b ? "禁用" : "启用";
        }
        /// <summary>
        /// 不支持反向转换。
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
    }

    /// <summary>
    /// 将布尔值转换为可见性：true -> Visible，false -> Collapsed。
    /// 支持通过 Invert 反转逻辑。
    /// </summary>
    public class BoolToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// 是否反转转换逻辑。为 true 时，true->Collapsed，false->Visible。
        /// </summary>
        public bool Invert { get; set; }
        /// <summary>
        /// 将布尔值转换为 Visibility。
        /// </summary>
        /// <param name="value">布尔值，表示条件成立与否。</param>
        /// <param name="targetType">目标类型。</param>
        /// <param name="parameter">可选参数。</param>
        /// <param name="culture">文化信息。</param>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool bValue = value is bool b && b;
            if (Invert) bValue = !bValue;
            return bValue ? Visibility.Visible : Visibility.Collapsed;

        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
    }
    #endregion
}
