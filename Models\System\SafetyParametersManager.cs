using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace PEMTestSystem.Models.System
{
    /// <summary>
    /// 安全参数管理器 - 管理所有实验模式的安全参数配置
    /// </summary>
    public class SafetyParametersManager : INotifyPropertyChanged
    {
        private GlobalSafetyParameters _globalParameters;
        private ConstantCurrentSafetyParameters _constantCurrentParameters;
        private ConstantVoltageSafetyParameters _constantVoltageParameters;
        private LinearScanSafetyParameters _linearScanParameters;

        public SafetyParametersManager()
        {
            InitializeDefaultParameters();
        }

        /// <summary>
        /// 全局安全参数
        /// </summary>
        public GlobalSafetyParameters GlobalParameters
        {
            get => _globalParameters;
            set
            {
                _globalParameters = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 恒流模式安全参数
        /// </summary>
        public ConstantCurrentSafetyParameters ConstantCurrentParameters
        {
            get => _constantCurrentParameters;
            set
            {
                _constantCurrentParameters = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 恒压模式安全参数
        /// </summary>
        public ConstantVoltageSafetyParameters ConstantVoltageParameters
        {
            get => _constantVoltageParameters;
            set
            {
                _constantVoltageParameters = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 线扫模式安全参数
        /// </summary>
        public LinearScanSafetyParameters LinearScanParameters
        {
            get => _linearScanParameters;
            set
            {
                _linearScanParameters = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 初始化默认参数
        /// </summary>
        private void InitializeDefaultParameters()
        {
            _globalParameters = new GlobalSafetyParameters();
            _constantCurrentParameters = new ConstantCurrentSafetyParameters();
            _constantVoltageParameters = new ConstantVoltageSafetyParameters();
            _linearScanParameters = new LinearScanSafetyParameters();
        }

        /// <summary>
        /// 根据实验模式获取对应的安全参数
        /// </summary>
        /// <param name="mode">实验模式</param>
        /// <returns>对应的安全参数对象</returns>
        public ExperimentSafetyParameters GetParametersForMode(ExperimentMode mode)
        {
            return mode switch
            {
                ExperimentMode.ConstantCurrent => ConstantCurrentParameters,
                ExperimentMode.ConstantVoltage => ConstantVoltageParameters,
                ExperimentMode.LinearScan => LinearScanParameters,
                _ => throw new ArgumentException($"未知的实验模式: {mode}")
            };
        }

        /// <summary>
        /// 验证参数值是否在安全范围内
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <param name="value">参数值</param>
        /// <param name="mode">实验模式</param>
        /// <returns>验证结果和错误信息</returns>
        public (bool IsValid, string ErrorMessage) ValidateParameter(string parameterName, double value, ExperimentMode mode)
        {
            try
            {
                var parameters = GetParametersForMode(mode);
                
                return parameterName.ToLower() switch
                {
                    "temperature" or "targettemperature" => ValidateTemperature(value),
                    "voltage" or "targetvoltage" => ValidateVoltage(value),
                    "current" or "targetcurrent" => ValidateCurrent(value, mode),
                    "samplinginterval" => ValidateSamplingInterval(value),
                    "duration" => ValidateDuration(value, parameters),
                    "scanrate" when mode == ExperimentMode.LinearScan => ValidateScanRate(value),
                    "scantime" when mode == ExperimentMode.LinearScan => ValidateScanTime(value),
                    "holdtime" when mode == ExperimentMode.LinearScan => ValidateHoldTime(value),
                    _ => (true, string.Empty)
                };
            }
            catch (Exception ex)
            {
                return (false, $"参数验证异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证温度参数
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateTemperature(double value)
        {
            if (value < GlobalParameters.MinTemperature || value > GlobalParameters.MaxTemperature)
            {
                return (false, $"温度应在 {GlobalParameters.MinTemperature}-{GlobalParameters.MaxTemperature}°C 范围内");
            }
            return (true, string.Empty);
        }

        /// <summary>
        /// 验证电压参数
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateVoltage(double value)
        {
            if (value < GlobalParameters.MinVoltage || value > GlobalParameters.MaxVoltage)
            {
                return (false, $"电压应在 {GlobalParameters.MinVoltage}-{GlobalParameters.MaxVoltage}V 范围内");
            }
            return (true, string.Empty);
        }

        /// <summary>
        /// 验证电流参数
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateCurrent(double value, ExperimentMode mode)
        {
            if (value < GlobalParameters.MinCurrent || value > GlobalParameters.MaxCurrent)
            {
                return (false, $"电流应在 {GlobalParameters.MinCurrent}-{GlobalParameters.MaxCurrent}A 范围内");
            }

            // 根据模式进行额外验证
            if (mode == ExperimentMode.ConstantCurrent)
            {
                var ccParams = ConstantCurrentParameters;
                if (value > ccParams.TargetCurrentUpperLimit)
                {
                    return (false, $"恒流模式目标电流不能超过 {ccParams.TargetCurrentUpperLimit}A");
                }
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// 验证采样间隔
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateSamplingInterval(double value)
        {
            if (value < 0.1 || value > 60)
            {
                return (false, "采样间隔应在 0.1-60 秒范围内");
            }
            return (true, string.Empty);
        }

        /// <summary>
        /// 验证实验持续时间
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateDuration(double value, ExperimentSafetyParameters parameters)
        {
            if (value < parameters.MinExperimentDuration || value > parameters.MaxExperimentDuration)
            {
                return (false, $"实验持续时间应在 {parameters.MinExperimentDuration}-{parameters.MaxExperimentDuration} 秒范围内");
            }
            return (true, string.Empty);
        }

        /// <summary>
        /// 验证扫描速率
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateScanRate(double value)
        {
            var scanParams = LinearScanParameters;
            if (value < scanParams.MinScanRate || value > scanParams.MaxScanRate)
            {
                return (false, $"扫描速率应在 {scanParams.MinScanRate}-{scanParams.MaxScanRate} V/s 范围内");
            }
            return (true, string.Empty);
        }

        /// <summary>
        /// 验证扫描时间
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateScanTime(double value)
        {
            var scanParams = LinearScanParameters;
            if (value < scanParams.MinScanTime || value > scanParams.MaxScanTime)
            {
                return (false, $"扫描时间应在 {scanParams.MinScanTime}-{scanParams.MaxScanTime} 秒范围内");
            }
            return (true, string.Empty);
        }

        /// <summary>
        /// 验证保持时间
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateHoldTime(double value)
        {
            var scanParams = LinearScanParameters;
            if (value < scanParams.MinHoldTime || value > scanParams.MaxHoldTime)
            {
                return (false, $"保持时间应在 {scanParams.MinHoldTime}-{scanParams.MaxHoldTime} 秒范围内");
            }
            return (true, string.Empty);
        }

        /// <summary>
        /// 重置为默认参数
        /// </summary>
        public void ResetToDefaults()
        {
            InitializeDefaultParameters();
            OnPropertyChanged(nameof(GlobalParameters));
            OnPropertyChanged(nameof(ConstantCurrentParameters));
            OnPropertyChanged(nameof(ConstantVoltageParameters));
            OnPropertyChanged(nameof(LinearScanParameters));
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 实验模式枚举
    /// </summary>
    public enum ExperimentMode
    {
        ConstantCurrent,
        ConstantVoltage,
        LinearScan
    }
}
